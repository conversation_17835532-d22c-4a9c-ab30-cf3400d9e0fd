//@version=6
strategy('3MA Strategy v12', overlay = true, pyramiding = 1, initial_capital = 1000, default_qty_type = strategy.percent_of_equity, default_qty_value = 100, process_orders_on_close = true)

// ======== 输入参数 ========
// 交易方向
trade_direction = input.string('both', '交易方向', options = ['long', 'short', 'both'])
is_long_allowed = trade_direction == 'long' or trade_direction == 'both'
is_short_allowed = trade_direction == 'short' or trade_direction == 'both'

// 添加信号确认参数
var group_signal = '信号确认'
enable_signal_confirm = input.bool(false, '启用信号确认', group = group_signal)

// 风险控制
var group_risk = '风险控制'
enable_hard_drawdown = input.bool(true, "启用硬回撤限制", group = group_risk)
input_max_drawdown = input.float(8, "硬性最大回撤(%)", group = group_risk, minval=1, maxval=30)
enable_drawdown = input.bool(true, '启用最大回撤控制', group = group_risk)
max_drawdown_pct = input.float(4, '最大回撤%', group = group_risk, minval = 1, maxval = 100) / 100 
enable_daily_limit = input.bool(true, '启用每日交易限制', group = group_risk)
max_daily_trades = input.int(8, '最大每日交易次数', group = group_risk, minval = 1)
recovery_threshold_pct = input.float(2, '恢复阈值%', group = group_risk, minval = 0.1, maxval = 100) / 100
enable_daily_loss = input.bool(true, '启用每日最大亏损', group = group_risk)
daily_max_loss_pct = input.float(2, '每日最大亏损%', group = group_risk, minval = 1, maxval = 50) / 100
min_pause_hours = input.int(8, '最小暂停时间(小时)', group = group_risk, minval = 1)

// ======== 策略风控 ========
max_drawdown_value = enable_hard_drawdown ? input_max_drawdown : na
strategy.risk.max_drawdown(max_drawdown_value, strategy.percent_of_equity)

// 止损参数
var group_stop = '止损参数'
selected_stop_type = input.string('固定止损', title = '止损类型', options = ['固定止损', '移动止损', '无'], group = group_stop)

// 添加盈亏平衡参数
breakeven = input.bool(title="使用盈亏平衡", defval=true, group = group_stop)
bet_target = input.int(title="盈亏平衡目标倍数", defval=2, options=[1, 2, 3, 4], group = group_stop)
commission = input.float(title="手续费%", defval=0.07, minval=0, maxval=1, group = group_stop)

// 添加分批止盈参数
enable_pyramid_exit = input.bool(false, '启用分批止盈', group = group_stop)
exit1_profit = input.float(5.0, '第一止盈位%', group = group_stop, minval = 0, maxval = 1000) / 100
exit1_qty = input.float(0.15, '第一止盈比例', group = group_stop, minval = 0, maxval = 1.0)
exit2_profit = input.float(10.0, '第二止盈位%', group = group_stop, minval = 0, maxval = 1000) / 100
exit2_qty = input.float(0.25, '第二止盈比例', group = group_stop, minval = 0, maxval = 1.0)
exit3_profit = input.float(20.0, '第三止盈位%', group = group_stop, minval = 0, maxval = 2000) / 100
exit3_qty = input.float(0.3, '第三止盈比例', group = group_stop, minval = 0, maxval = 1.0)

fixed_stop_loss_pct = input.float(0.8, '固定止损%', group = group_stop, minval = 0, maxval = 100, inline = 'fixed_stop') / 100
fixed_take_profit_pct = input.float(30.0, '固定止盈%', group = group_stop, minval = 0, maxval = 2000, inline = 'fixed_stop') / 100
trailing_start_pct = input.float(6.0, '移动止盈触发%', group = group_stop, minval = 0, maxval = 2000, inline = 'trailing_stop') / 100
trailing_stop_pct = input.float(1.0, '移动止损回调%', group = group_stop, minval = 0, maxval = 100, inline = 'trailing_stop') / 100
immediate_stop = input.bool(true, '使用开仓初始止损', group = group_stop)
initial_stop_loss_pct = input.float(0.6, '初始止损%', group = group_stop, minval = 0, maxval = 100) / 100

// 均线参数
var group_ma = '均线参数'
ma1_period = input.int(30, title = 'MA1周期', group = group_ma, minval = 1)
ma2_period = input.int(45, title = 'MA2周期', group = group_ma, minval = 1)
ma3_period = input.int(60, title = 'MA3周期', group = group_ma, minval = 1)


// 趋势过滤
var group_filter = '趋势过滤'
enable_adx_filter = input.bool(false, '启用ADX趋势过滤', group = group_filter)
di_length = input.int(14, 'DI周期', group = group_filter, minval = 1)
adx_smoothing = input.int(14, 'ADX平滑周期', group = group_filter, minval = 1)
adx_threshold = input.int(25, 'ADX强度阈值', group = group_filter, minval = 15, maxval = 50)

// 仓位管理
var group_position = '仓位管理'
enable_atr_sizing = input.bool(false, '启用波动仓位管理', group = group_position)
atr_period = input.int(14, 'ATR周期', group = group_position, minval = 5, maxval = 50)
risk_percent = input.float(1.0, '风险百分比%', group = group_position, minval = 0.1, maxval = 100) / 100

// 时间退出
var group_time_exit = '时间退出'
enable_time_exit = input.bool(true, '启用最大持仓时间', group = group_time_exit)
max_holding_hours = input.int(24, '最大持仓时间(小时)', group = group_time_exit, minval = 1)

// 时间设置
var group_time = '回测时间'
testStartYear = input.int(2025, '开始年份', group = group_time)
testStartMonth = input.int(1, '开始月份', group = group_time)
testStartDay = input.int(1, '开始日', group = group_time)
testStopYear = input.int(2099, '结束年份', group = group_time)
testStopMonth = input.int(12, '结束月份', group = group_time)
testStopDay = input.int(31, '结束日', group = group_time)

// 交易时段
var group_trading_time = '交易时段'
tradingtime = input.bool(true, '使用交易时段', group = group_trading_time)
start_hour = input.int(8, '开始小时', group = group_trading_time)
start_minute = input.int(0, '开始分钟', group = group_trading_time)
end_hour = input.int(23, '结束小时', group = group_trading_time)
end_minute = input.int(59, '结束分钟', group = group_trading_time)
timezone_offset = input.int(8, '时区偏移', group = group_trading_time)

// ======== 全局变量 ========
varip float entry_price = na
varip float highestHigh = na
varip float lowestLow = na
varip int daily_trades = 0
varip float daily_equity_start = strategy.initial_capital
varip string last_trade_day = na
varip bool trading_paused = false
varip int pause_start_time = 0
varip float base_equity_after_stop = na
varip float max_normal_equity = strategy.initial_capital
varip bool recovery_mode = false
varip float current_equity = na
varip int current_time = na
varip string current_exchange_day = na
varip int entry_bar_time = na
varip bool daily_loss_breached = false
varip bool exit1_triggered = false
varip bool exit2_triggered = false
varip bool exit3_triggered = false
varip bool base_long_detected = false
varip bool base_short_detected = false
varip float key_long_high = na
varip float key_short_low = na
varip bool hard_drawdown_triggered = false

// ======== 核心逻辑 ========
in_test_period() =>
    time >= timestamp(testStartYear, testStartMonth, testStartDay, 0, 0) and time <= timestamp(testStopYear, testStopMonth, testStopDay, 23, 59)

in_trading_time() =>
    exchange_time = time + timezone_offset * 60 * 60 * 1000
    exchange_hour = hour(exchange_time)
    exchange_minute = minute(exchange_time)
    time_ok = exchange_hour > start_hour or exchange_hour == start_hour and exchange_minute >= start_minute
    time_ok := time_ok and (exchange_hour < end_hour or exchange_hour == end_hour and exchange_minute <= end_minute)
    time_ok

current_equity := strategy.equity
current_time := time
current_exchange_day := str.format('{0}-{1}-{2}', year(time + timezone_offset * 60 * 60 * 1000), month(time + timezone_offset * 60 * 60 * 1000), dayofmonth(time + timezone_offset * 60 * 60 * 1000))

[dip, din, adx_value] = ta.dmi(di_length, adx_smoothing)
valid_trend_long = not enable_adx_filter or adx_value >= adx_threshold and dip > din
valid_trend_short = not enable_adx_filter or adx_value >= adx_threshold and din > dip

atr_value = ta.atr(atr_period)
position_size = enable_atr_sizing ? strategy.equity * risk_percent / (atr_value * syminfo.pointvalue) : strategy.equity / close

if strategy.position_size == 0
    entry_bar_time := na
    entry_bar_time
else if na(entry_bar_time)
    entry_bar_time := current_time
    entry_bar_time

holding_hours = (current_time - entry_bar_time) / (1000 * 60 * 60)
time_exit_condition = enable_time_exit and holding_hours >= max_holding_hours

// 每日重置逻辑
if current_exchange_day != last_trade_day
    daily_trades := 0
    daily_equity_start := strategy.equity
    last_trade_day := current_exchange_day
    trading_paused := false
    daily_loss_breached := false
    max_normal_equity := strategy.initial_capital
    max_normal_equity

// 每日亏损检测
daily_loss = enable_daily_loss ? (daily_equity_start - current_equity) / daily_equity_start : 0.0
daily_loss_breached := enable_daily_loss and daily_loss >= daily_max_loss_pct and daily_max_loss_pct > 0
if daily_loss_breached
    trading_paused := true
    trading_paused

// 回撤控制

// 在每根K线结束时检查硬回撤
if enable_hard_drawdown and strategy.equity <= strategy.initial_capital * (1 - input_max_drawdown / 100)
    hard_drawdown_triggered := true

// 触发后平仓并暂停交易
if hard_drawdown_triggered and strategy.position_size != 0
    strategy.close_all(comment = "硬回撤平仓")
    trading_paused := true

if enable_drawdown
    if not recovery_mode
        max_normal_equity := math.max(max_normal_equity, current_equity)
        drawdown = (max_normal_equity - current_equity) / max_normal_equity
        if drawdown >= max_drawdown_pct and strategy.position_size != 0
            strategy.close_all()
            base_equity_after_stop := current_equity
            recovery_mode := true
            trading_paused := true
            pause_start_time := current_time
            pause_start_time
    if recovery_mode
        recovery_return = (current_equity - base_equity_after_stop) / base_equity_after_stop
        pause_duration = (current_time - pause_start_time) / (1000 * 60 * 60)
        if recovery_return >= recovery_threshold_pct or pause_duration >= min_pause_hours
            recovery_mode := false
            trading_paused := false
            max_normal_equity := current_equity
            max_normal_equity

can_trade = not trading_paused and (not enable_daily_limit or daily_trades < max_daily_trades)

// 交易信号
ma1 = ta.sma(close, ma1_period)
ma2 = ta.sma(close, ma2_period)
ma3 = ta.sma(close, ma3_period)

// 基础信号条件
// base_long_condition = barstate.isconfirmed and close > ma1 and close > ma2 and close > ma3 and ma1 > ma2 and ma2 > ma3 and valid_trend_long
// base_short_condition = barstate.isconfirmed and close < ma1 and close < ma2 and close < ma3 and ma1 < ma2 and ma2 < ma3 and valid_trend_short
base_long_condition = barstate.isconfirmed and close > ma1 and close > ma2 and close > ma3 and valid_trend_long
base_short_condition = barstate.isconfirmed and close < ma1 and close < ma2 and close < ma3 and valid_trend_short

// 信号确认逻辑 - 修改为保持关键K线直到突破/跌破均线
if base_long_condition and strategy.position_size == 0 and not base_long_detected
    base_long_detected := true
    key_long_high := high
    
if base_short_condition and strategy.position_size == 0 and not base_short_detected
    base_short_detected := true
    key_short_low := low

// 最终信号
long_entry = enable_signal_confirm ? (base_long_detected and close > key_long_high) : base_long_condition
short_entry = enable_signal_confirm ? (base_short_detected and close < key_short_low) : base_short_condition

// 重置信号状态 - 只有在价格跌破/突破任意均线时才重置
if base_long_detected and (long_entry or close < ma1 or close < ma2 or close < ma3)
    base_long_detected := false
    
if base_short_detected and (short_entry or close > ma1 or close > ma2 or close > ma3)
    base_short_detected := false

// 当开仓后也需要重置状态
if strategy.position_size != 0
    base_long_detected := false
    base_short_detected := false

close_long = barstate.isconfirmed and (close < ma1 or close < ma2 or close < ma3)
close_short = barstate.isconfirmed and (close > ma1 or close > ma2 or close > ma3)

// 执行交易
if in_test_period() and can_trade and (not tradingtime or in_trading_time())
    if strategy.position_size == 0
        if is_long_allowed and long_entry
            entry_price := close
            highestHigh := high
            // 重置分批止盈状态
            exit1_triggered := false
            exit2_triggered := false
            exit3_triggered := false
            
            strategy.entry('Long', strategy.long, qty = position_size, comment = '开多')
            daily_trades := daily_trades + 1
            if selected_stop_type == '固定止损'
                strategy.exit('Long Exit', 'Long', stop = entry_price * (1 - fixed_stop_loss_pct), limit = entry_price * (1 + fixed_take_profit_pct), comment = '固损')
            else if selected_stop_type == '移动止损'
                strategy.exit('Long Trail', 'Long', stop = entry_price * (1 - initial_stop_loss_pct), comment = '移损')

        else if is_short_allowed and short_entry
            entry_price := close
            lowestLow := low
            // 重置分批止盈状态
            exit1_triggered := false
            exit2_triggered := false
            exit3_triggered := false
            
            strategy.entry('Short', strategy.short, qty = position_size, comment = '开空')
            daily_trades := daily_trades + 1
            if selected_stop_type == '固定止损'
                strategy.exit('Short Exit', 'Short', stop = entry_price * (1 + fixed_stop_loss_pct), limit = entry_price * (1 - fixed_take_profit_pct), comment = '固损')
            else if selected_stop_type == '移动止损'
                strategy.exit('Short Trail', 'Short', stop = entry_price * (1 + initial_stop_loss_pct), comment = '移损')

    // 更新跟踪止损
    if strategy.position_size > 0 and not trading_paused
        highestHigh := math.max(high, highestHigh)
        if selected_stop_type == '固定止损'
            float breakeven_target = entry_price * (1 + fixed_stop_loss_pct * bet_target)
            float breakeven_stop = entry_price * (1 + commission / 100)
            float stop_price = entry_price * (1 - fixed_stop_loss_pct)
            if breakeven and high >= breakeven_target
                stop_price := breakeven_stop
            strategy.exit('Long Exit', 'Long', stop = stop_price, limit = not enable_pyramid_exit ? entry_price * (1 + fixed_take_profit_pct) : na, comment = stop_price == breakeven_stop ? '盈亏平衡' : '固定止损止盈')
        else if selected_stop_type == '移动止损'
            float breakeven_target = entry_price * (1 + initial_stop_loss_pct * bet_target)
            float breakeven_stop = entry_price * (1 + commission / 100)
            trail_activate = highestHigh >= entry_price * (1 + trailing_start_pct)
            float trail_stop = trail_activate ? highestHigh * (1 - trailing_stop_pct) : entry_price * (1 - initial_stop_loss_pct)
            float stop_price = trail_stop
            if breakeven and high >= breakeven_target
                stop_price := math.max(breakeven_stop, trail_stop)
            strategy.exit('Long Trail', 'Long', stop = stop_price, comment = stop_price == breakeven_stop ? '盈亏平衡' : '移损')
            
        // 添加多头分批止盈逻辑
        if enable_pyramid_exit
            float unrealized_pct = (close - entry_price) / entry_price
            float current_position = math.abs(strategy.position_size)
            
            if not exit1_triggered and unrealized_pct >= exit1_profit
                float exit1_size = math.min(exit1_qty * current_position, current_position)
                strategy.order('Long Exit1', strategy.short, qty = exit1_size, comment = '一阶止盈')
                exit1_triggered := true
            
            if not exit2_triggered and unrealized_pct >= exit2_profit
                float exit2_size = math.min(exit2_qty * current_position, current_position)
                strategy.order('Long Exit2', strategy.short, qty = exit2_size, comment = '二阶止盈')
                exit2_triggered := true
            
            if not exit3_triggered and unrealized_pct >= exit3_profit
                strategy.close_all(comment = '三阶全平')
                exit3_triggered := true
    
            if exit1_qty == 1.0 or exit2_qty == 1.0 or exit3_qty == 1.0
                strategy.close_all(comment = '全额止盈')
                
    else if strategy.position_size < 0 and not trading_paused
        lowestLow := math.min(low, lowestLow)
        if selected_stop_type == '固定止损'
            float breakeven_target = entry_price * (1 - fixed_stop_loss_pct * bet_target)
            float breakeven_stop = entry_price * (1 - commission / 100)
            float stop_price = entry_price * (1 + fixed_stop_loss_pct)
            if breakeven and low <= breakeven_target
                stop_price := breakeven_stop
            strategy.exit('Short Exit', 'Short', stop = stop_price, limit = not enable_pyramid_exit ? entry_price * (1 - fixed_take_profit_pct) : na, comment = stop_price == breakeven_stop ? '盈亏平衡' : '固定止损止盈')
        else if selected_stop_type == '移动止损'
            float breakeven_target = entry_price * (1 - initial_stop_loss_pct * bet_target)
            float breakeven_stop = entry_price * (1 - commission / 100)
            trail_activate = lowestLow <= entry_price * (1 - trailing_start_pct)
            float trail_stop = trail_activate ? lowestLow * (1 + trailing_stop_pct) : entry_price * (1 + initial_stop_loss_pct)
            float stop_price = trail_stop
            if breakeven and low <= breakeven_target
                stop_price := math.min(breakeven_stop, trail_stop)
            strategy.exit('Short Trail', 'Short', stop = stop_price, comment = stop_price == breakeven_stop ? '盈亏平衡' : '移损')
            
        // 添加空头分批止盈逻辑
        if enable_pyramid_exit
            float unrealized_pct_short = (entry_price - close) / entry_price
            float current_position = math.abs(strategy.position_size)
            
            if not exit1_triggered and unrealized_pct_short >= exit1_profit
                float exit1_size = math.min(exit1_qty * current_position, current_position)
                strategy.order('Short Exit1', strategy.long, qty = exit1_size, comment = '一阶止盈')
                exit1_triggered := true
            
            if not exit2_triggered and unrealized_pct_short >= exit2_profit
                float exit2_size = math.min(exit2_qty * current_position, current_position)
                strategy.order('Short Exit2', strategy.long, qty = exit2_size, comment = '二阶止盈')
                exit2_triggered := true
            
            if not exit3_triggered and unrealized_pct_short >= exit3_profit
                strategy.close_all(comment = '三阶全平')
                exit3_triggered := true
    
            if exit1_qty == 1.0 or exit2_qty == 1.0 or exit3_qty == 1.0
                strategy.close_all(comment = '全额止盈')
    
// 平仓逻辑
if strategy.position_size > 0 and (close_long or time_exit_condition)
    strategy.close('Long', comment = '平多')
if strategy.position_size < 0 and (close_short or time_exit_condition)
    strategy.close('Short', comment = '平空')

// 可视化
plot(ma1, color = color.blue)
plot(ma2, color = color.orange)
plot(ma3, color = color.purple)






//@version=6
strategy('3MA Strategy v12', overlay = true, pyramiding = 1, initial_capital = 1000, default_qty_type = strategy.percent_of_equity, default_qty_value = 100, process_orders_on_close = true)

// ======== 输入参数 ========
// 交易方向
trade_direction = input.string('both', '交易方向', options = ['long', 'short', 'both'])
is_long_allowed = trade_direction == 'long' or trade_direction == 'both'
is_short_allowed = trade_direction == 'short' or trade_direction == 'both'

// 风险控制
var group_risk = '风险控制'
enable_drawdown = input.bool(true, '启用最大回撤控制', group = group_risk)
max_drawdown_pct = input.float(4, '最大回撤%', group = group_risk, minval = 0, maxval = 100) / 100 
enable_hard_drawdown = input.bool(true, "启用硬回撤限制", group = group_risk)
input_max_drawdown = input.float(8, "硬性最大回撤(%)", group = group_risk, minval=3, maxval=30)
enable_daily_limit = input.bool(true, '启用每日交易限制', group = group_risk)
max_daily_trades = input.int(5, '最大每日交易次数', group = group_risk, minval = 1)
recovery_threshold_pct = input.float(2, '恢复阈值%', group = group_risk, minval = 0.1, maxval = 100) / 100
enable_daily_loss = input.bool(false, '启用每日最大亏损', group = group_risk)
daily_max_loss_pct = input.float(2, '每日最大亏损%', group = group_risk, minval = 1, maxval = 50) / 100
min_pause_hours = input.int(8, '最小暂停时间(小时)', group = group_risk, minval = 1)

// ======== 策略风控 ========
max_drawdown_value = enable_hard_drawdown ? input_max_drawdown : na
strategy.risk.max_drawdown(max_drawdown_value, strategy.percent_of_equity)

// 均线参数
var group_ma = '均线参数'
ma1_period = input.int(30, title = 'MA1周期', group = group_ma, minval = 1)
ma2_period = input.int(45, title = 'MA2周期', group = group_ma, minval = 1)
ma3_period = input.int(60, title = 'MA3周期', group = group_ma, minval = 1)

// 止损参数
var group_stop = '止损参数'
selected_stop_type = input.string('固定止损', title = '止损类型', options = ['固定止损', '移动止损', '无'], group = group_stop)

// 添加盈亏平衡参数
breakeven = input.bool(title="使用盈亏平衡", defval=false, group = group_stop)
bet_target = input.int(title="盈亏平衡目标倍数", defval=2, options=[1, 2, 3, 4], group = group_stop)
commission = input.float(title="手续费%", defval=0.07, minval=0, maxval=1, group = group_stop)

// 添加分批止盈参数
enable_pyramid_exit = input.bool(false, '启用分批止盈', group = group_stop)
exit1_profit = input.float(5.0, '第一止盈位%', group = group_stop, minval = 0, maxval = 1000) / 100
exit1_qty = input.float(0.15, '第一止盈比例', group = group_stop, minval = 0, maxval = 1.0)
exit2_profit = input.float(10.0, '第二止盈位%', group = group_stop, minval = 0, maxval = 1000) / 100
exit2_qty = input.float(0.25, '第二止盈比例', group = group_stop, minval = 0, maxval = 1.0)
exit3_profit = input.float(20.0, '第三止盈位%', group = group_stop, minval = 0, maxval = 2000) / 100
exit3_qty = input.float(0.3, '第三止盈比例', group = group_stop, minval = 0, maxval = 1.0)

fixed_stop_loss_pct = input.float(1.0, '固定止损%', group = group_stop, minval = 0, maxval = 100, inline = 'fixed_stop') / 100
fixed_take_profit_pct = input.float(30.0, '固定止盈%', group = group_stop, minval = 0, maxval = 2000, inline = 'fixed_stop') / 100
trailing_start_pct = input.float(2.0, '移动止盈触发%', group = group_stop, minval = 0, maxval = 2000, inline = 'trailing_stop') / 100
trailing_stop_pct = input.float(1.0, '移动止损回调%', group = group_stop, minval = 0, maxval = 100, inline = 'trailing_stop') / 100
immediate_stop = input.bool(true, '使用开仓初始止损', group = group_stop)
initial_stop_loss_pct = input.float(0.6, '初始止损%', group = group_stop, minval = 0, maxval = 100) / 100

// 添加信号确认参数
var group_signal = '信号确认'
enable_signal_confirm = input.bool(false, '启用信号确认', group = group_signal)

// 趋势过滤
var group_filter = '趋势过滤'
enable_adx_filter = input.bool(true, '启用ADX趋势过滤', group = group_filter)
di_length = input.int(14, 'DI周期', group = group_filter, minval = 1)
adx_smoothing = input.int(14, 'ADX平滑周期', group = group_filter, minval = 1)
adx_threshold = input.int(25, 'ADX强度阈值', group = group_filter, minval = 15, maxval = 50)

// 仓位管理
var group_position = '仓位管理'
enable_atr_sizing = input.bool(true, '启用波动仓位管理', group = group_position)
atr_period = input.int(14, 'ATR周期', group = group_position, minval = 5, maxval = 50)
risk_percent = input.float(1.0, '风险百分比%', group = group_position, minval = 0.1, maxval = 100) / 100

// 时间退出
var group_time_exit = '时间退出'
enable_time_exit = input.bool(true, '启用最大持仓时间', group = group_time_exit)
max_holding_hours = input.int(24, '最大持仓时间(小时)', group = group_time_exit, minval = 1)

// 时间设置
var group_time = '回测时间'
testStartYear = input.int(2025, '开始年份', group = group_time)
testStartMonth = input.int(1, '开始月份', group = group_time)
testStartDay = input.int(1, '开始日', group = group_time)
testStopYear = input.int(2099, '结束年份', group = group_time)
testStopMonth = input.int(12, '结束月份', group = group_time)
testStopDay = input.int(31, '结束日', group = group_time)

// 交易时段
var group_trading_time = '交易时段'
tradingtime = input.bool(true, '使用交易时段', group = group_trading_time)
start_hour = input.int(8, '开始小时', group = group_trading_time)
start_minute = input.int(0, '开始分钟', group = group_trading_time)
end_hour = input.int(23, '结束小时', group = group_trading_time)
end_minute = input.int(59, '结束分钟', group = group_trading_time)
timezone_offset = input.int(8, '时区偏移', group = group_trading_time)

// ======== 全局变量 ========
varip float entry_price = na
varip float highestHigh = na
varip float lowestLow = na
varip int daily_trades = 0
varip float daily_equity_start = strategy.initial_capital
varip string last_trade_day = na
varip bool trading_paused = false
varip int pause_start_time = 0
varip float base_equity_after_stop = na
varip float max_normal_equity = strategy.initial_capital
varip bool recovery_mode = false
varip float current_equity = na
varip int current_time = na
varip string current_exchange_day = na
varip int entry_bar_time = na
varip bool daily_loss_breached = false
varip bool exit1_triggered = false
varip bool exit2_triggered = false
varip bool exit3_triggered = false
varip bool base_long_detected = false
varip bool base_short_detected = false
varip float key_long_high = na
varip float key_short_low = na
varip bool hard_drawdown_triggered = false

// ======== 核心逻辑 ========
in_test_period() =>
    time >= timestamp(testStartYear, testStartMonth, testStartDay, 0, 0) and time <= timestamp(testStopYear, testStopMonth, testStopDay, 23, 59)

in_trading_time() =>
    exchange_time = time + timezone_offset * 60 * 60 * 1000
    exchange_hour = hour(exchange_time)
    exchange_minute = minute(exchange_time)
    time_ok = exchange_hour > start_hour or exchange_hour == start_hour and exchange_minute >= start_minute
    time_ok := time_ok and (exchange_hour < end_hour or exchange_hour == end_hour and exchange_minute <= end_minute)
    time_ok

current_equity := strategy.equity
current_time := time
current_exchange_day := str.format('{0}-{1}-{2}', year(time + timezone_offset * 60 * 60 * 1000), month(time + timezone_offset * 60 * 60 * 1000), dayofmonth(time + timezone_offset * 60 * 60 * 1000))

[dip, din, adx_value] = ta.dmi(di_length, adx_smoothing)
valid_trend_long = not enable_adx_filter or adx_value >= adx_threshold and dip > din
valid_trend_short = not enable_adx_filter or adx_value >= adx_threshold and din > dip

atr_value = ta.atr(atr_period)
position_size = enable_atr_sizing ? strategy.equity * risk_percent / (atr_value * syminfo.pointvalue) : strategy.equity / close

if strategy.position_size == 0
    entry_bar_time := na
    entry_bar_time
else if na(entry_bar_time)
    entry_bar_time := current_time
    entry_bar_time

holding_hours = (current_time - entry_bar_time) / (1000 * 60 * 60)
time_exit_condition = enable_time_exit and holding_hours >= max_holding_hours

// 每日重置逻辑
if current_exchange_day != last_trade_day
    daily_trades := 0
    daily_equity_start := strategy.equity
    last_trade_day := current_exchange_day
    trading_paused := false
    daily_loss_breached := false
    max_normal_equity := strategy.initial_capital
    max_normal_equity

// 每日亏损检测
daily_loss = enable_daily_loss ? (daily_equity_start - current_equity) / daily_equity_start : 0.0
daily_loss_breached := enable_daily_loss and daily_loss >= daily_max_loss_pct and daily_max_loss_pct > 0
if daily_loss_breached
    trading_paused := true
    trading_paused

// 回撤控制

// 在每根K线结束时检查硬回撤
if enable_hard_drawdown and strategy.equity <= strategy.initial_capital * (1 - input_max_drawdown / 100)
    hard_drawdown_triggered := true

// 触发后平仓并暂停交易
if hard_drawdown_triggered and strategy.position_size != 0
    strategy.close_all(comment = "硬回撤平仓")
    trading_paused := true

if enable_drawdown
    if not recovery_mode
        max_normal_equity := math.max(max_normal_equity, current_equity)
        drawdown = (max_normal_equity - current_equity) / max_normal_equity
        if drawdown >= max_drawdown_pct and strategy.position_size != 0
            strategy.close_all()
            base_equity_after_stop := current_equity
            recovery_mode := true
            trading_paused := true
            pause_start_time := current_time
            pause_start_time
    if recovery_mode
        recovery_return = (current_equity - base_equity_after_stop) / base_equity_after_stop
        pause_duration = (current_time - pause_start_time) / (1000 * 60 * 60)
        if recovery_return >= recovery_threshold_pct or pause_duration >= min_pause_hours
            recovery_mode := false
            trading_paused := false
            max_normal_equity := current_equity
            max_normal_equity

can_trade = not trading_paused and (not enable_daily_limit or daily_trades < max_daily_trades)

// 交易信号
ma1 = ta.sma(close, ma1_period)
ma2 = ta.sma(close, ma2_period)
ma3 = ta.sma(close, ma3_period)

// 基础信号条件
base_long_condition = barstate.isconfirmed and close > ma1 and close > ma2 and close > ma3 and ma1 > ma2 and ma2 > ma3 and valid_trend_long
base_short_condition = barstate.isconfirmed and close < ma1 and close < ma2 and close < ma3 and ma1 < ma2 and ma2 < ma3 and valid_trend_short

// 信号确认逻辑 - 修改为保持关键K线直到突破/跌破均线
if base_long_condition and strategy.position_size == 0 and not base_long_detected
    base_long_detected := true
    key_long_high := high
    
if base_short_condition and strategy.position_size == 0 and not base_short_detected
    base_short_detected := true
    key_short_low := low

// 最终信号
long_entry = enable_signal_confirm ? (base_long_detected and close > key_long_high) : base_long_condition
short_entry = enable_signal_confirm ? (base_short_detected and close < key_short_low) : base_short_condition

// 重置信号状态 - 只有在价格跌破/突破任意均线时才重置
if base_long_detected and (long_entry or close < ma1 or close < ma2 or close < ma3)
    base_long_detected := false
    
if base_short_detected and (short_entry or close > ma1 or close > ma2 or close > ma3)
    base_short_detected := false

// 当开仓后也需要重置状态
if strategy.position_size != 0
    base_long_detected := false
    base_short_detected := false

close_long = barstate.isconfirmed and (close < ma1 or close < ma2 or close < ma3)
close_short = barstate.isconfirmed and (close > ma1 or close > ma2 or close > ma3)

// 执行交易
if in_test_period() and can_trade and (not tradingtime or in_trading_time())
    if strategy.position_size == 0
        if is_long_allowed and long_entry
            entry_price := close
            highestHigh := high
            // 重置分批止盈状态
            exit1_triggered := false
            exit2_triggered := false
            exit3_triggered := false
            
            strategy.entry('Long', strategy.long, qty = position_size, comment = '开多')
            daily_trades := daily_trades + 1
            if selected_stop_type == '固定止损'
                strategy.exit('Long Exit', 'Long', stop = entry_price * (1 - fixed_stop_loss_pct), limit = entry_price * (1 + fixed_take_profit_pct), comment = '固损')
            else if selected_stop_type == '移动止损'
                strategy.exit('Long Trail', 'Long', stop = entry_price * (1 - initial_stop_loss_pct), comment = '移损')

        else if is_short_allowed and short_entry
            entry_price := close
            lowestLow := low
            // 重置分批止盈状态
            exit1_triggered := false
            exit2_triggered := false
            exit3_triggered := false
            
            strategy.entry('Short', strategy.short, qty = position_size, comment = '开空')
            daily_trades := daily_trades + 1
            if selected_stop_type == '固定止损'
                strategy.exit('Short Exit', 'Short', stop = entry_price * (1 + fixed_stop_loss_pct), limit = entry_price * (1 - fixed_take_profit_pct), comment = '固损')
            else if selected_stop_type == '移动止损'
                strategy.exit('Short Trail', 'Short', stop = entry_price * (1 + initial_stop_loss_pct), comment = '移损')

    // 更新跟踪止损
    if strategy.position_size > 0 and not trading_paused
        highestHigh := math.max(high, highestHigh)
        if selected_stop_type == '固定止损'
            float breakeven_target = entry_price * (1 + fixed_stop_loss_pct * bet_target)
            float breakeven_stop = entry_price * (1 + commission / 100)
            float stop_price = entry_price * (1 - fixed_stop_loss_pct)
            if breakeven and high >= breakeven_target
                stop_price := breakeven_stop
            strategy.exit('Long Exit', 'Long', stop = stop_price, limit = not enable_pyramid_exit ? entry_price * (1 + fixed_take_profit_pct) : na, comment = stop_price == breakeven_stop ? '盈亏平衡' : '固定止损止盈')
        else if selected_stop_type == '移动止损'
            float breakeven_target = entry_price * (1 + initial_stop_loss_pct * bet_target)
            float breakeven_stop = entry_price * (1 + commission / 100)
            trail_activate = highestHigh >= entry_price * (1 + trailing_start_pct)
            float trail_stop = trail_activate ? highestHigh * (1 - trailing_stop_pct) : entry_price * (1 - initial_stop_loss_pct)
            float stop_price = trail_stop
            if breakeven and high >= breakeven_target
                stop_price := math.max(breakeven_stop, trail_stop)
            strategy.exit('Long Trail', 'Long', stop = stop_price, comment = stop_price == breakeven_stop ? '盈亏平衡' : '移损')
            
        // 添加多头分批止盈逻辑
        if enable_pyramid_exit
            float unrealized_pct = (close - entry_price) / entry_price
            float current_position = math.abs(strategy.position_size)
            
            if not exit1_triggered and unrealized_pct >= exit1_profit
                float exit1_size = math.min(exit1_qty * current_position, current_position)
                strategy.order('Long Exit1', strategy.short, qty = exit1_size, comment = '一阶止盈')
                exit1_triggered := true
            
            if not exit2_triggered and unrealized_pct >= exit2_profit
                float exit2_size = math.min(exit2_qty * current_position, current_position)
                strategy.order('Long Exit2', strategy.short, qty = exit2_size, comment = '二阶止盈')
                exit2_triggered := true
            
            if not exit3_triggered and unrealized_pct >= exit3_profit
                strategy.close_all(comment = '三阶全平')
                exit3_triggered := true
    
            if exit1_qty == 1.0 or exit2_qty == 1.0 or exit3_qty == 1.0
                strategy.close_all(comment = '全额止盈')
                
    else if strategy.position_size < 0 and not trading_paused
        lowestLow := math.min(low, lowestLow)
        if selected_stop_type == '固定止损'
            float breakeven_target = entry_price * (1 - fixed_stop_loss_pct * bet_target)
            float breakeven_stop = entry_price * (1 - commission / 100)
            float stop_price = entry_price * (1 + fixed_stop_loss_pct)
            if breakeven and low <= breakeven_target
                stop_price := breakeven_stop
            strategy.exit('Short Exit', 'Short', stop = stop_price, limit = not enable_pyramid_exit ? entry_price * (1 - fixed_take_profit_pct) : na, comment = stop_price == breakeven_stop ? '盈亏平衡' : '固定止损止盈')
        else if selected_stop_type == '移动止损'
            float breakeven_target = entry_price * (1 - initial_stop_loss_pct * bet_target)
            float breakeven_stop = entry_price * (1 - commission / 100)
            trail_activate = lowestLow <= entry_price * (1 - trailing_start_pct)
            float trail_stop = trail_activate ? lowestLow * (1 + trailing_stop_pct) : entry_price * (1 + initial_stop_loss_pct)
            float stop_price = trail_stop
            if breakeven and low <= breakeven_target
                stop_price := math.min(breakeven_stop, trail_stop)
            strategy.exit('Short Trail', 'Short', stop = stop_price, comment = stop_price == breakeven_stop ? '盈亏平衡' : '移损')
            
        // 添加空头分批止盈逻辑
        if enable_pyramid_exit
            float unrealized_pct_short = (entry_price - close) / entry_price
            float current_position = math.abs(strategy.position_size)
            
            if not exit1_triggered and unrealized_pct_short >= exit1_profit
                float exit1_size = math.min(exit1_qty * current_position, current_position)
                strategy.order('Short Exit1', strategy.long, qty = exit1_size, comment = '一阶止盈')
                exit1_triggered := true
            
            if not exit2_triggered and unrealized_pct_short >= exit2_profit
                float exit2_size = math.min(exit2_qty * current_position, current_position)
                strategy.order('Short Exit2', strategy.long, qty = exit2_size, comment = '二阶止盈')
                exit2_triggered := true
            
            if not exit3_triggered and unrealized_pct_short >= exit3_profit
                strategy.close_all(comment = '三阶全平')
                exit3_triggered := true
    
            if exit1_qty == 1.0 or exit2_qty == 1.0 or exit3_qty == 1.0
                strategy.close_all(comment = '全额止盈')
    
// 平仓逻辑
if strategy.position_size > 0 and (close_long or time_exit_condition)
    strategy.close('Long', comment = '平多')
if strategy.position_size < 0 and (close_short or time_exit_condition)
    strategy.close('Short', comment = '平空')

// 可视化
plot(ma1, color = color.blue)
plot(ma2, color = color.orange)
plot(ma3, color = color.purple)











//@version=6
strategy('3MA Strategy v12', overlay = true, pyramiding = 1, initial_capital = 1000, default_qty_type = strategy.percent_of_equity, default_qty_value = 100, process_orders_on_close = true)

// ======== 输入参数 ========
// 交易方向
trade_direction = input.string('both', '交易方向', options = ['long', 'short', 'both'])
is_long_allowed = trade_direction == 'long' or trade_direction == 'both'
is_short_allowed = trade_direction == 'short' or trade_direction == 'both'

// 均线参数
var group_ma = '均线参数'
ma1_period = input.int(30, title = 'MA1周期', group = group_ma, minval = 1)
ma2_period = input.int(45, title = 'MA2周期', group = group_ma, minval = 1)
ma3_period = input.int(60, title = 'MA3周期', group = group_ma, minval = 1)

// 止损参数
var group_stop = '止损参数'
selected_stop_type = input.string('固定止损', title = '止损类型', options = ['固定止损', '移动止损', '无'], group = group_stop)

// 添加盈亏平衡参数
breakeven = input.bool(title="使用盈亏平衡", defval=false, group = group_stop)
bet_target = input.int(title="盈亏平衡目标倍数", defval=2, options=[1, 2, 3, 4], group = group_stop)
commission = input.float(title="手续费%", defval=0.07, minval=0, maxval=1, group = group_stop)

// 添加分批止盈参数
enable_pyramid_exit = input.bool(false, '启用分批止盈', group = group_stop)
exit1_profit = input.float(5.0, '第一止盈位%', group = group_stop, minval = 0, maxval = 1000) / 100
exit1_qty = input.float(0.15, '第一止盈比例', group = group_stop, minval = 0, maxval = 1.0)
exit2_profit = input.float(10.0, '第二止盈位%', group = group_stop, minval = 0, maxval = 1000) / 100
exit2_qty = input.float(0.25, '第二止盈比例', group = group_stop, minval = 0, maxval = 1.0)
exit3_profit = input.float(20.0, '第三止盈位%', group = group_stop, minval = 0, maxval = 2000) / 100
exit3_qty = input.float(0.3, '第三止盈比例', group = group_stop, minval = 0, maxval = 1.0)

fixed_stop_loss_pct = input.float(1.0, '固定止损%', group = group_stop, minval = 0, maxval = 100, inline = 'fixed_stop') / 100
fixed_take_profit_pct = input.float(30.0, '固定止盈%', group = group_stop, minval = 0, maxval = 2000, inline = 'fixed_stop') / 100
trailing_start_pct = input.float(2.0, '移动止盈触发%', group = group_stop, minval = 0, maxval = 2000, inline = 'trailing_stop') / 100
trailing_stop_pct = input.float(1.0, '移动止损回调%', group = group_stop, minval = 0, maxval = 100, inline = 'trailing_stop') / 100
immediate_stop = input.bool(true, '使用开仓初始止损', group = group_stop)
initial_stop_loss_pct = input.float(0.6, '初始止损%', group = group_stop, minval = 0, maxval = 100) / 100

// 添加信号确认参数
var group_signal = '信号确认'
enable_signal_confirm = input.bool(false, '启用信号确认', group = group_signal)

// 风险控制
var group_risk = '风险控制'
enable_daily_limit = input.bool(true, '启用每日交易限制', group = group_risk)
max_daily_trades = input.int(5, '最大每日交易次数', group = group_risk, minval = 1)
enable_drawdown = input.bool(true, '启用最大回撤控制', group = group_risk)
max_drawdown_pct = input.float(4, '最大回撤%', group = group_risk, minval = 0, maxval = 100) / 100
recovery_threshold_pct = input.float(2, '恢复阈值%', group = group_risk, minval = 0.1, maxval = 100) / 100
enable_daily_loss = input.bool(false, '启用每日最大亏损', group = group_risk)
daily_max_loss_pct = input.float(3, '每日最大亏损%', group = group_risk, minval = 0, maxval = 50) / 100
min_pause_hours = input.int(8, '最小暂停时间(小时)', group = group_risk, minval = 1)

// 趋势过滤
var group_filter = '趋势过滤'
enable_adx_filter = input.bool(true, '启用ADX趋势过滤', group = group_filter)
di_length = input.int(14, 'DI周期', group = group_filter, minval = 1)
adx_smoothing = input.int(14, 'ADX平滑周期', group = group_filter, minval = 1)
adx_threshold = input.int(25, 'ADX强度阈值', group = group_filter, minval = 15, maxval = 50)

// 仓位管理
var group_position = '仓位管理'
enable_atr_sizing = input.bool(true, '启用波动仓位管理', group = group_position)
atr_period = input.int(14, 'ATR周期', group = group_position, minval = 5, maxval = 50)
risk_percent = input.float(1.0, '风险百分比%', group = group_position, minval = 0.1, maxval = 100) / 100

// 时间退出
var group_time_exit = '时间退出'
enable_time_exit = input.bool(true, '启用最大持仓时间', group = group_time_exit)
max_holding_hours = input.int(24, '最大持仓时间(小时)', group = group_time_exit, minval = 1)

// 时间设置
var group_time = '回测时间'
testStartYear = input.int(2025, '开始年份', group = group_time)
testStartMonth = input.int(1, '开始月份', group = group_time)
testStartDay = input.int(1, '开始日', group = group_time)
testStopYear = input.int(2099, '结束年份', group = group_time)
testStopMonth = input.int(12, '结束月份', group = group_time)
testStopDay = input.int(31, '结束日', group = group_time)

// 交易时段
var group_trading_time = '交易时段'
tradingtime = input.bool(true, '使用交易时段', group = group_trading_time)
start_hour = input.int(8, '开始小时', group = group_trading_time)
start_minute = input.int(0, '开始分钟', group = group_trading_time)
end_hour = input.int(23, '结束小时', group = group_trading_time)
end_minute = input.int(59, '结束分钟', group = group_trading_time)
timezone_offset = input.int(8, '时区偏移', group = group_trading_time)

// ======== 全局变量 ========
varip float entry_price = na
varip float highestHigh = na
varip float lowestLow = na
varip int daily_trades = 0
varip float daily_equity_start = strategy.initial_capital
varip string last_trade_day = na
varip bool trading_paused = false
varip int pause_start_time = 0
varip float base_equity_after_stop = na
varip float max_normal_equity = strategy.initial_capital
varip bool recovery_mode = false
varip float current_equity = na
varip int current_time = na
varip string current_exchange_day = na
varip int entry_bar_time = na
varip bool daily_loss_breached = false
// 添加分批止盈状态变量
varip bool exit1_triggered = false
varip bool exit2_triggered = false
varip bool exit3_triggered = false
// 添加信号确认状态变量
varip bool base_long_detected = false
varip bool base_short_detected = false
varip float key_long_high = na
varip float key_short_low = na


// ======== 核心逻辑 ========
in_test_period() =>
    time >= timestamp(testStartYear, testStartMonth, testStartDay, 0, 0) and time <= timestamp(testStopYear, testStopMonth, testStopDay, 23, 59)

in_trading_time() =>
    exchange_time = time + timezone_offset * 60 * 60 * 1000
    exchange_hour = hour(exchange_time)
    exchange_minute = minute(exchange_time)
    time_ok = exchange_hour > start_hour or exchange_hour == start_hour and exchange_minute >= start_minute
    time_ok := time_ok and (exchange_hour < end_hour or exchange_hour == end_hour and exchange_minute <= end_minute)
    time_ok

current_equity := strategy.equity
current_time := time
current_exchange_day := str.format('{0}-{1}-{2}', year(time + timezone_offset * 60 * 60 * 1000), month(time + timezone_offset * 60 * 60 * 1000), dayofmonth(time + timezone_offset * 60 * 60 * 1000))

[dip, din, adx_value] = ta.dmi(di_length, adx_smoothing)
valid_trend_long = not enable_adx_filter or adx_value >= adx_threshold and dip > din
valid_trend_short = not enable_adx_filter or adx_value >= adx_threshold and din > dip

atr_value = ta.atr(atr_period)
position_size = enable_atr_sizing ? strategy.equity * risk_percent / (atr_value * syminfo.pointvalue) : strategy.equity / close

if strategy.position_size == 0
    entry_bar_time := na
    entry_bar_time
else if na(entry_bar_time)
    entry_bar_time := current_time
    entry_bar_time

holding_hours = (current_time - entry_bar_time) / (1000 * 60 * 60)
time_exit_condition = enable_time_exit and holding_hours >= max_holding_hours

// 每日重置逻辑
if current_exchange_day != last_trade_day
    daily_trades := 0
    daily_equity_start := strategy.equity
    last_trade_day := current_exchange_day
    trading_paused := false
    daily_loss_breached := false
    max_normal_equity := strategy.initial_capital
    max_normal_equity

// 每日亏损检测
daily_loss = enable_daily_loss ? (daily_equity_start - current_equity) / daily_equity_start : 0.0
daily_loss_breached := enable_daily_loss and daily_loss >= daily_max_loss_pct and daily_max_loss_pct > 0
if daily_loss_breached
    trading_paused := true
    trading_paused

// 回撤控制
if enable_drawdown
    if not recovery_mode
        max_normal_equity := math.max(max_normal_equity, current_equity)
        drawdown = (max_normal_equity - current_equity) / max_normal_equity
        if drawdown >= max_drawdown_pct and strategy.position_size != 0
            strategy.close_all()
            base_equity_after_stop := current_equity
            recovery_mode := true
            trading_paused := true
            pause_start_time := current_time
            pause_start_time
    if recovery_mode
        recovery_return = (current_equity - base_equity_after_stop) / base_equity_after_stop
        pause_duration = (current_time - pause_start_time) / (1000 * 60 * 60)
        if recovery_return >= recovery_threshold_pct or pause_duration >= min_pause_hours
            recovery_mode := false
            trading_paused := false
            max_normal_equity := current_equity
            max_normal_equity

can_trade = not trading_paused and (not enable_daily_limit or daily_trades < max_daily_trades)

// 交易信号
ma1 = ta.sma(close, ma1_period)
ma2 = ta.sma(close, ma2_period)
ma3 = ta.sma(close, ma3_period)

// 基础信号条件
base_long_condition = barstate.isconfirmed and close > ma1 and close > ma2 and close > ma3 and ma1 > ma2 and ma2 > ma3 and valid_trend_long
base_short_condition = barstate.isconfirmed and close < ma1 and close < ma2 and close < ma3 and ma1 < ma2 and ma2 < ma3 and valid_trend_short

// 信号确认逻辑 - 修改为保持关键K线直到突破/跌破均线
if base_long_condition and strategy.position_size == 0 and not base_long_detected
    base_long_detected := true
    key_long_high := high
    
if base_short_condition and strategy.position_size == 0 and not base_short_detected
    base_short_detected := true
    key_short_low := low

// 最终信号
long_entry = enable_signal_confirm ? (base_long_detected and close > key_long_high) : base_long_condition
short_entry = enable_signal_confirm ? (base_short_detected and close < key_short_low) : base_short_condition

// 重置信号状态 - 只有在价格跌破/突破任意均线时才重置
if base_long_detected and (long_entry or close < ma1 or close < ma2 or close < ma3)
    base_long_detected := false
    
if base_short_detected and (short_entry or close > ma1 or close > ma2 or close > ma3)
    base_short_detected := false

// 当开仓后也需要重置状态
if strategy.position_size != 0
    base_long_detected := false
    base_short_detected := false

close_long = barstate.isconfirmed and (close < ma1 or close < ma2 or close < ma3)
close_short = barstate.isconfirmed and (close > ma1 or close > ma2 or close > ma3)

// 执行交易
if in_test_period() and can_trade and (not tradingtime or in_trading_time())
    if strategy.position_size == 0
        if is_long_allowed and long_entry
            entry_price := close
            highestHigh := high
            // 重置分批止盈状态
            exit1_triggered := false
            exit2_triggered := false
            exit3_triggered := false
            
            strategy.entry('Long', strategy.long, qty = position_size, comment = '开多')
            daily_trades := daily_trades + 1
            if selected_stop_type == '固定止损'
                strategy.exit('Long Exit', 'Long', stop = entry_price * (1 - fixed_stop_loss_pct), limit = entry_price * (1 + fixed_take_profit_pct), comment = '固损')
            else if selected_stop_type == '移动止损'
                strategy.exit('Long Trail', 'Long', stop = entry_price * (1 - initial_stop_loss_pct), comment = '移损')

        else if is_short_allowed and short_entry
            entry_price := close
            lowestLow := low
            // 重置分批止盈状态
            exit1_triggered := false
            exit2_triggered := false
            exit3_triggered := false
            
            strategy.entry('Short', strategy.short, qty = position_size, comment = '开空')
            daily_trades := daily_trades + 1
            if selected_stop_type == '固定止损'
                strategy.exit('Short Exit', 'Short', stop = entry_price * (1 + fixed_stop_loss_pct), limit = entry_price * (1 - fixed_take_profit_pct), comment = '固损')
            else if selected_stop_type == '移动止损'
                strategy.exit('Short Trail', 'Short', stop = entry_price * (1 + initial_stop_loss_pct), comment = '移损')

    // 更新跟踪止损
    if strategy.position_size > 0 and not trading_paused
        highestHigh := math.max(high, highestHigh)
        if selected_stop_type == '固定止损'
            float breakeven_target = entry_price * (1 + fixed_stop_loss_pct * bet_target)
            float breakeven_stop = entry_price * (1 + commission / 100)
            float stop_price = entry_price * (1 - fixed_stop_loss_pct)
            if breakeven and high >= breakeven_target
                stop_price := breakeven_stop
            strategy.exit('Long Exit', 'Long', stop = stop_price, limit = not enable_pyramid_exit ? entry_price * (1 + fixed_take_profit_pct) : na, comment = stop_price == breakeven_stop ? '盈亏平衡' : '固定止损止盈')
        else if selected_stop_type == '移动止损'
            float breakeven_target = entry_price * (1 + initial_stop_loss_pct * bet_target)
            float breakeven_stop = entry_price * (1 + commission / 100)
            trail_activate = highestHigh >= entry_price * (1 + trailing_start_pct)
            float trail_stop = trail_activate ? highestHigh * (1 - trailing_stop_pct) : entry_price * (1 - initial_stop_loss_pct)
            float stop_price = trail_stop
            if breakeven and high >= breakeven_target
                stop_price := math.max(breakeven_stop, trail_stop)
            strategy.exit('Long Trail', 'Long', stop = stop_price, comment = stop_price == breakeven_stop ? '盈亏平衡' : '移损')
            
        // 添加多头分批止盈逻辑
        if enable_pyramid_exit
            float unrealized_pct = (close - entry_price) / entry_price
            float current_position = math.abs(strategy.position_size)
            
            if not exit1_triggered and unrealized_pct >= exit1_profit
                float exit1_size = math.min(exit1_qty * current_position, current_position)
                strategy.order('Long Exit1', strategy.short, qty = exit1_size, comment = '一阶止盈')
                exit1_triggered := true
            
            if not exit2_triggered and unrealized_pct >= exit2_profit
                float exit2_size = math.min(exit2_qty * current_position, current_position)
                strategy.order('Long Exit2', strategy.short, qty = exit2_size, comment = '二阶止盈')
                exit2_triggered := true
            
            if not exit3_triggered and unrealized_pct >= exit3_profit
                strategy.close_all(comment = '三阶全平')
                exit3_triggered := true
    
            if exit1_qty == 1.0 or exit2_qty == 1.0 or exit3_qty == 1.0
                strategy.close_all(comment = '全额止盈')
                
    else if strategy.position_size < 0 and not trading_paused
        lowestLow := math.min(low, lowestLow)
        if selected_stop_type == '固定止损'
            float breakeven_target = entry_price * (1 - fixed_stop_loss_pct * bet_target)
            float breakeven_stop = entry_price * (1 - commission / 100)
            float stop_price = entry_price * (1 + fixed_stop_loss_pct)
            if breakeven and low <= breakeven_target
                stop_price := breakeven_stop
            strategy.exit('Short Exit', 'Short', stop = stop_price, limit = not enable_pyramid_exit ? entry_price * (1 - fixed_take_profit_pct) : na, comment = stop_price == breakeven_stop ? '盈亏平衡' : '固定止损止盈')
        else if selected_stop_type == '移动止损'
            float breakeven_target = entry_price * (1 - initial_stop_loss_pct * bet_target)
            float breakeven_stop = entry_price * (1 - commission / 100)
            trail_activate = lowestLow <= entry_price * (1 - trailing_start_pct)
            float trail_stop = trail_activate ? lowestLow * (1 + trailing_stop_pct) : entry_price * (1 + initial_stop_loss_pct)
            float stop_price = trail_stop
            if breakeven and low <= breakeven_target
                stop_price := math.min(breakeven_stop, trail_stop)
            strategy.exit('Short Trail', 'Short', stop = stop_price, comment = stop_price == breakeven_stop ? '盈亏平衡' : '移损')
            
        // 添加空头分批止盈逻辑
        if enable_pyramid_exit
            float unrealized_pct_short = (entry_price - close) / entry_price
            float current_position = math.abs(strategy.position_size)
            
            if not exit1_triggered and unrealized_pct_short >= exit1_profit
                float exit1_size = math.min(exit1_qty * current_position, current_position)
                strategy.order('Short Exit1', strategy.long, qty = exit1_size, comment = '一阶止盈')
                exit1_triggered := true
            
            if not exit2_triggered and unrealized_pct_short >= exit2_profit
                float exit2_size = math.min(exit2_qty * current_position, current_position)
                strategy.order('Short Exit2', strategy.long, qty = exit2_size, comment = '二阶止盈')
                exit2_triggered := true
            
            if not exit3_triggered and unrealized_pct_short >= exit3_profit
                strategy.close_all(comment = '三阶全平')
                exit3_triggered := true
    
            if exit1_qty == 1.0 or exit2_qty == 1.0 or exit3_qty == 1.0
                strategy.close_all(comment = '全额止盈')
    
    // 平仓逻辑
    if strategy.position_size > 0 and (close_long or time_exit_condition)
        strategy.close('Long', comment = '平多')
    if strategy.position_size < 0 and (close_short or time_exit_condition)
        strategy.close('Short', comment = '平空')

// 可视化
plot(ma1, color = color.blue)
plot(ma2, color = color.orange)
plot(ma3, color = color.purple)





//@version=6
strategy('3MA Strategy v12', overlay = true, pyramiding = 1, initial_capital = 1000, default_qty_type = strategy.percent_of_equity, default_qty_value = 100, process_orders_on_close = true)

// ======== 输入参数 ========
// 交易方向
trade_direction = input.string('both', '交易方向', options = ['long', 'short', 'both'])
is_long_allowed = trade_direction == 'long' or trade_direction == 'both'
is_short_allowed = trade_direction == 'short' or trade_direction == 'both'

// 均线参数
var group_ma = '均线参数'
ma1_period = input.int(30, title = 'MA1周期', group = group_ma, minval = 1)
ma2_period = input.int(45, title = 'MA2周期', group = group_ma, minval = 1)
ma3_period = input.int(60, title = 'MA3周期', group = group_ma, minval = 1)

// 止损参数
var group_stop = '止损参数'
selected_stop_type = input.string('固定止损', title = '止损类型', options = ['固定止损', '移动止损', '无'], group = group_stop)

// 添加盈亏平衡参数
breakeven = input.bool(title="使用盈亏平衡", defval=false, group = group_stop)
bet_target = input.int(title="盈亏平衡目标倍数", defval=1, options=[1, 2, 3, 4], group = group_stop)
commission = input.float(title="手续费%", defval=0.1, minval=0, maxval=1, group = group_stop)

// 添加分批止盈参数
enable_pyramid_exit = input.bool(false, '启用分批止盈', group = group_stop)
exit1_profit = input.float(50.0, '第一止盈位%', group = group_stop, minval = 0, maxval = 1000) / 100
exit1_qty = input.float(0.3, '第一止盈比例', group = group_stop, minval = 0, maxval = 1.0)
exit2_profit = input.float(100.0, '第二止盈位%', group = group_stop, minval = 0, maxval = 1000) / 100
exit2_qty = input.float(0.3, '第二止盈比例', group = group_stop, minval = 0, maxval = 1.0)
exit3_profit = input.float(200.0, '第三止盈位%', group = group_stop, minval = 0, maxval = 2000) / 100
exit3_qty = input.float(1.0, '第三止盈比例', group = group_stop, minval = 0, maxval = 1.0)

fixed_stop_loss_pct = input.float(1.0, '固定止损%', group = group_stop, minval = 0, maxval = 100, inline = 'fixed_stop') / 100
fixed_take_profit_pct = input.float(300.0, '固定止盈%', group = group_stop, minval = 0, maxval = 2000, inline = 'fixed_stop') / 100
trailing_start_pct = input.float(20.0, '移动止盈触发%', group = group_stop, minval = 0, maxval = 2000, inline = 'trailing_stop') / 100
trailing_stop_pct = input.float(1.0, '移动止损回调%', group = group_stop, minval = 0, maxval = 100, inline = 'trailing_stop') / 100
immediate_stop = input.bool(true, '使用开仓初始止损', group = group_stop)
initial_stop_loss_pct = input.float(0.6, '初始止损%', group = group_stop, minval = 0, maxval = 100) / 100

// 风险控制
var group_risk = '风险控制'
enable_daily_limit = input.bool(false, '启用每日交易限制', group = group_risk)
max_daily_trades = input.int(5, '最大每日交易次数', group = group_risk, minval = 1)
enable_drawdown = input.bool(true, '启用最大回撤控制', group = group_risk)
max_drawdown_pct = input.float(4, '最大回撤%', group = group_risk, minval = 0, maxval = 100) / 100
recovery_threshold_pct = input.float(2, '恢复阈值%', group = group_risk, minval = 0.1, maxval = 100) / 100
enable_daily_loss = input.bool(false, '启用每日最大亏损', group = group_risk)
daily_max_loss_pct = input.float(5, '每日最大亏损%', group = group_risk, minval = 0, maxval = 50) / 100
min_pause_hours = input.int(12, '最小暂停时间(小时)', group = group_risk, minval = 1)

// 趋势过滤
var group_filter = '趋势过滤'
enable_adx_filter = input.bool(true, '启用ADX趋势过滤', group = group_filter)
di_length = input.int(14, 'DI周期', group = group_filter, minval = 1)
adx_smoothing = input.int(14, 'ADX平滑周期', group = group_filter, minval = 1)
adx_threshold = input.int(25, 'ADX强度阈值', group = group_filter, minval = 15, maxval = 50)

// 仓位管理
var group_position = '仓位管理'
enable_atr_sizing = input.bool(true, '启用波动仓位管理', group = group_position)
atr_period = input.int(14, 'ATR周期', group = group_position, minval = 5, maxval = 50)
risk_percent = input.float(1.0, '风险百分比%', group = group_position, minval = 0.1, maxval = 100) / 100

// 时间退出
var group_time_exit = '时间退出'
enable_time_exit = input.bool(true, '启用最大持仓时间', group = group_time_exit)
max_holding_hours = input.int(24, '最大持仓时间(小时)', group = group_time_exit, minval = 1)

// 时间设置
var group_time = '回测时间'
testStartYear = input.int(2025, '开始年份', group = group_time)
testStartMonth = input.int(1, '开始月份', group = group_time)
testStartDay = input.int(1, '开始日', group = group_time)
testStopYear = input.int(2099, '结束年份', group = group_time)
testStopMonth = input.int(12, '结束月份', group = group_time)
testStopDay = input.int(31, '结束日', group = group_time)

// 交易时段
var group_trading_time = '交易时段'
tradingtime = input.bool(true, '使用交易时段', group = group_trading_time)
start_hour = input.int(8, '开始小时', group = group_trading_time)
start_minute = input.int(0, '开始分钟', group = group_trading_time)
end_hour = input.int(23, '结束小时', group = group_trading_time)
end_minute = input.int(59, '结束分钟', group = group_trading_time)
timezone_offset = input.int(8, '时区偏移', group = group_trading_time)

// ======== 全局变量 ========
varip float entry_price = na
varip float highestHigh = na
varip float lowestLow = na
varip int daily_trades = 0
varip float daily_equity_start = strategy.initial_capital
varip string last_trade_day = na
varip bool trading_paused = false
varip int pause_start_time = 0
varip float base_equity_after_stop = na
varip float max_normal_equity = strategy.initial_capital
varip bool recovery_mode = false
varip float current_equity = na
varip int current_time = na
varip string current_exchange_day = na
varip int entry_bar_time = na
varip bool daily_loss_breached = false

// 添加分批止盈状态变量
varip bool exit1_triggered = false
varip bool exit2_triggered = false
varip bool exit3_triggered = false

// ======== 核心逻辑 ========
in_test_period() =>
    time >= timestamp(testStartYear, testStartMonth, testStartDay, 0, 0) and time <= timestamp(testStopYear, testStopMonth, testStopDay, 23, 59)

in_trading_time() =>
    exchange_time = time + timezone_offset * 60 * 60 * 1000
    exchange_hour = hour(exchange_time)
    exchange_minute = minute(exchange_time)
    time_ok = exchange_hour > start_hour or exchange_hour == start_hour and exchange_minute >= start_minute
    time_ok := time_ok and (exchange_hour < end_hour or exchange_hour == end_hour and exchange_minute <= end_minute)
    time_ok

current_equity := strategy.equity
current_time := time
current_exchange_day := str.format('{0}-{1}-{2}', year(time + timezone_offset * 60 * 60 * 1000), month(time + timezone_offset * 60 * 60 * 1000), dayofmonth(time + timezone_offset * 60 * 60 * 1000))

[dip, din, adx_value] = ta.dmi(di_length, adx_smoothing)
valid_trend_long = not enable_adx_filter or adx_value >= adx_threshold and dip > din
valid_trend_short = not enable_adx_filter or adx_value >= adx_threshold and din > dip

atr_value = ta.atr(atr_period)
position_size = enable_atr_sizing ? strategy.equity * risk_percent / (atr_value * syminfo.pointvalue) : strategy.equity / close

if strategy.position_size == 0
    entry_bar_time := na
    entry_bar_time
else if na(entry_bar_time)
    entry_bar_time := current_time
    entry_bar_time

holding_hours = (current_time - entry_bar_time) / (1000 * 60 * 60)
time_exit_condition = enable_time_exit and holding_hours >= max_holding_hours

// 每日重置逻辑
if current_exchange_day != last_trade_day
    daily_trades := 0
    daily_equity_start := strategy.equity
    last_trade_day := current_exchange_day
    trading_paused := false
    daily_loss_breached := false
    max_normal_equity := strategy.initial_capital
    max_normal_equity

// 每日亏损检测
daily_loss = enable_daily_loss ? (daily_equity_start - current_equity) / daily_equity_start : 0.0
daily_loss_breached := enable_daily_loss and daily_loss >= daily_max_loss_pct and daily_max_loss_pct > 0
if daily_loss_breached
    trading_paused := true
    trading_paused

// 回撤控制
if enable_drawdown
    if not recovery_mode
        max_normal_equity := math.max(max_normal_equity, current_equity)
        drawdown = (max_normal_equity - current_equity) / max_normal_equity
        if drawdown >= max_drawdown_pct and strategy.position_size != 0
            strategy.close_all()
            base_equity_after_stop := current_equity
            recovery_mode := true
            trading_paused := true
            pause_start_time := current_time
            pause_start_time
    if recovery_mode
        recovery_return = (current_equity - base_equity_after_stop) / base_equity_after_stop
        pause_duration = (current_time - pause_start_time) / (1000 * 60 * 60)
        if recovery_return >= recovery_threshold_pct or pause_duration >= min_pause_hours
            recovery_mode := false
            trading_paused := false
            max_normal_equity := current_equity
            max_normal_equity

can_trade = not trading_paused and (not enable_daily_limit or daily_trades < max_daily_trades)

// 交易信号
ma1 = ta.sma(close, ma1_period)
ma2 = ta.sma(close, ma2_period)
ma3 = ta.sma(close, ma3_period)
long_entry = barstate.isconfirmed and close > ma1 and close > ma2 and close > ma3 and ma1 > ma2 and ma2 > ma3 and valid_trend_long
short_entry = barstate.isconfirmed and close < ma1 and close < ma2 and close < ma3 and ma1 < ma2 and ma2 < ma3 and valid_trend_short
close_long = barstate.isconfirmed and (close < ma1 or close < ma2 or close < ma3)
close_short = barstate.isconfirmed and (close > ma1 or close > ma2 or close > ma3)

// 执行交易
if in_test_period() and can_trade and (not tradingtime or in_trading_time())
    if strategy.position_size == 0
        if is_long_allowed and long_entry
            entry_price := close
            highestHigh := high
            // 重置分批止盈状态
            exit1_triggered := false
            exit2_triggered := false
            exit3_triggered := false
            
            strategy.entry('Long', strategy.long, qty = position_size, comment = '开多')
            daily_trades := daily_trades + 1
            if selected_stop_type == '固定止损'
                strategy.exit('Long Exit', 'Long', stop = entry_price * (1 - fixed_stop_loss_pct), limit = entry_price * (1 + fixed_take_profit_pct), comment = '固损')
            else if selected_stop_type == '移动止损'
                strategy.exit('Long Trail', 'Long', stop = entry_price * (1 - initial_stop_loss_pct), comment = '移损')

        else if is_short_allowed and short_entry
            entry_price := close
            lowestLow := low
            // 重置分批止盈状态
            exit1_triggered := false
            exit2_triggered := false
            exit3_triggered := false
            
            strategy.entry('Short', strategy.short, qty = position_size, comment = '开空')
            daily_trades := daily_trades + 1
            if selected_stop_type == '固定止损'
                strategy.exit('Short Exit', 'Short', stop = entry_price * (1 + fixed_stop_loss_pct), limit = entry_price * (1 - fixed_take_profit_pct), comment = '固损')
            else if selected_stop_type == '移动止损'
                strategy.exit('Short Trail', 'Short', stop = entry_price * (1 + initial_stop_loss_pct), comment = '移损')

    // 更新跟踪止损
    if strategy.position_size > 0 and not trading_paused
        highestHigh := math.max(high, highestHigh)
        if selected_stop_type == '固定止损'
            float breakeven_target = entry_price * (1 + fixed_stop_loss_pct * bet_target)
            float breakeven_stop = entry_price * (1 + commission / 100)
            float stop_price = entry_price * (1 - fixed_stop_loss_pct)
            if breakeven and high >= breakeven_target
                stop_price := breakeven_stop
            strategy.exit('Long Exit', 'Long', stop = stop_price, limit = not enable_pyramid_exit ? entry_price * (1 + fixed_take_profit_pct) : na, comment = stop_price == breakeven_stop ? '盈亏平衡' : '固定止损止盈')
        else if selected_stop_type == '移动止损'
            float breakeven_target = entry_price * (1 + initial_stop_loss_pct * bet_target)
            float breakeven_stop = entry_price * (1 + commission / 100)
            trail_activate = highestHigh >= entry_price * (1 + trailing_start_pct)
            float trail_stop = trail_activate ? highestHigh * (1 - trailing_stop_pct) : entry_price * (1 - initial_stop_loss_pct)
            float stop_price = trail_stop
            if breakeven and high >= breakeven_target
                stop_price := math.max(breakeven_stop, trail_stop)
            strategy.exit('Long Trail', 'Long', stop = stop_price, comment = stop_price == breakeven_stop ? '盈亏平衡' : '移损')
            
        // 添加多头分批止盈逻辑
        if enable_pyramid_exit
            float unrealized_pct = (close - entry_price) / entry_price
            float current_position = math.abs(strategy.position_size)
            
            if not exit1_triggered and unrealized_pct >= exit1_profit
                float exit1_size = math.min(exit1_qty * current_position, current_position)
                strategy.order('Long Exit1', strategy.short, qty = exit1_size, comment = '一阶止盈')
                exit1_triggered := true
            
            if not exit2_triggered and unrealized_pct >= exit2_profit
                float exit2_size = math.min(exit2_qty * current_position, current_position)
                strategy.order('Long Exit2', strategy.short, qty = exit2_size, comment = '二阶止盈')
                exit2_triggered := true
            
            if not exit3_triggered and unrealized_pct >= exit3_profit
                strategy.close_all(comment = '三阶全平')
                exit3_triggered := true
    
            if exit1_qty == 1.0 or exit2_qty == 1.0 or exit3_qty == 1.0
                strategy.close_all(comment = '全额止盈')
                
    else if strategy.position_size < 0 and not trading_paused
        lowestLow := math.min(low, lowestLow)
        if selected_stop_type == '固定止损'
            float breakeven_target = entry_price * (1 - fixed_stop_loss_pct * bet_target)
            float breakeven_stop = entry_price * (1 - commission / 100)
            float stop_price = entry_price * (1 + fixed_stop_loss_pct)
            if breakeven and low <= breakeven_target
                stop_price := breakeven_stop
            strategy.exit('Short Exit', 'Short', stop = stop_price, limit = not enable_pyramid_exit ? entry_price * (1 - fixed_take_profit_pct) : na, comment = stop_price == breakeven_stop ? '盈亏平衡' : '固定止损止盈')
        else if selected_stop_type == '移动止损'
            float breakeven_target = entry_price * (1 - initial_stop_loss_pct * bet_target)
            float breakeven_stop = entry_price * (1 - commission / 100)
            trail_activate = lowestLow <= entry_price * (1 - trailing_start_pct)
            float trail_stop = trail_activate ? lowestLow * (1 + trailing_stop_pct) : entry_price * (1 + initial_stop_loss_pct)
            float stop_price = trail_stop
            if breakeven and low <= breakeven_target
                stop_price := math.min(breakeven_stop, trail_stop)
            strategy.exit('Short Trail', 'Short', stop = stop_price, comment = stop_price == breakeven_stop ? '盈亏平衡' : '移损')
            
        // 添加空头分批止盈逻辑
        if enable_pyramid_exit
            float unrealized_pct_short = (entry_price - close) / entry_price
            float current_position = math.abs(strategy.position_size)
            
            if not exit1_triggered and unrealized_pct_short >= exit1_profit
                float exit1_size = math.min(exit1_qty * current_position, current_position)
                strategy.order('Short Exit1', strategy.long, qty = exit1_size, comment = '一阶止盈')
                exit1_triggered := true
            
            if not exit2_triggered and unrealized_pct_short >= exit2_profit
                float exit2_size = math.min(exit2_qty * current_position, current_position)
                strategy.order('Short Exit2', strategy.long, qty = exit2_size, comment = '二阶止盈')
                exit2_triggered := true
            
            if not exit3_triggered and unrealized_pct_short >= exit3_profit
                strategy.close_all(comment = '三阶全平')
                exit3_triggered := true
    
            if exit1_qty == 1.0 or exit2_qty == 1.0 or exit3_qty == 1.0
                strategy.close_all(comment = '全额止盈')
    
    // 平仓逻辑
    if strategy.position_size > 0 and (close_long or time_exit_condition)
        strategy.close('Long', comment = '平多')
    if strategy.position_size < 0 and (close_short or time_exit_condition)
        strategy.close('Short', comment = '平空')

// 可视化
plot(ma1, color = color.blue)
plot(ma2, color = color.orange)
plot(ma3, color = color.purple)



// //@version=6
// strategy('3MA Strategy v12', overlay = true, pyramiding = 1, initial_capital = 1000, default_qty_type = strategy.percent_of_equity, default_qty_value = 100, process_orders_on_close = true)

// // ======== 输入参数 ========
// // 交易方向
// trade_direction = input.string('both', '交易方向', options = ['long', 'short', 'both'])
// is_long_allowed = trade_direction == 'long' or trade_direction == 'both'
// is_short_allowed = trade_direction == 'short' or trade_direction == 'both'

// // 均线参数
// var group_ma = '均线参数'
// ma1_period = input.int(30, title = 'MA1周期', group = group_ma, minval = 1)
// ma2_period = input.int(45, title = 'MA2周期', group = group_ma, minval = 1)
// ma3_period = input.int(60, title = 'MA3周期', group = group_ma, minval = 1)

// // 止损参数
// var group_stop = '止损参数'
// selected_stop_type = input.string('固定止损', title = '止损类型', options = ['固定止损', '移动止损', '无'], group = group_stop)

// // 添加分批止盈参数
// enable_pyramid_exit = input.bool(false, '启用分批止盈', group = group_stop)
// exit1_profit = input.float(50.0, '第一止盈位%', group = group_stop, minval = 0, maxval = 1000) / 100
// exit1_qty = input.float(0.3, '第一止盈比例', group = group_stop, minval = 0, maxval = 1.0)
// exit2_profit = input.float(100.0, '第二止盈位%', group = group_stop, minval = 0, maxval = 1000) / 100
// exit2_qty = input.float(0.3, '第二止盈比例', group = group_stop, minval = 0, maxval = 1.0)
// exit3_profit = input.float(200.0, '第三止盈位%', group = group_stop, minval = 0, maxval = 2000) / 100
// exit3_qty = input.float(1.0, '第三止盈比例', group = group_stop, minval = 0, maxval = 1.0)

// fixed_stop_loss_pct = input.float(1.0, '固定止损%', group = group_stop, minval = 0, maxval = 100, inline = 'fixed_stop') / 100
// fixed_take_profit_pct = input.float(300.0, '固定止盈%', group = group_stop, minval = 0, maxval = 2000, inline = 'fixed_stop') / 100
// trailing_start_pct = input.float(20.0, '移动止盈触发%', group = group_stop, minval = 0, maxval = 2000, inline = 'trailing_stop') / 100
// trailing_stop_pct = input.float(1.0, '移动止损回调%', group = group_stop, minval = 0, maxval = 100, inline = 'trailing_stop') / 100
// immediate_stop = input.bool(true, '使用开仓初始止损', group = group_stop)
// initial_stop_loss_pct = input.float(0.6, '初始止损%', group = group_stop, minval = 0, maxval = 100) / 100

// // 风险控制
// var group_risk = '风险控制'
// enable_daily_limit = input.bool(false, '启用每日交易限制', group = group_risk)
// max_daily_trades = input.int(5, '最大每日交易次数', group = group_risk, minval = 1)
// enable_drawdown = input.bool(true, '启用最大回撤控制', group = group_risk)
// max_drawdown_pct = input.float(4, '最大回撤%', group = group_risk, minval = 0, maxval = 100) / 100
// recovery_threshold_pct = input.float(2, '恢复阈值%', group = group_risk, minval = 0.1, maxval = 100) / 100
// enable_daily_loss = input.bool(false, '启用每日最大亏损', group = group_risk)
// daily_max_loss_pct = input.float(5, '每日最大亏损%', group = group_risk, minval = 0, maxval = 50) / 100
// min_pause_hours = input.int(12, '最小暂停时间(小时)', group = group_risk, minval = 1)

// // 趋势过滤
// var group_filter = '趋势过滤'
// enable_adx_filter = input.bool(true, '启用ADX趋势过滤', group = group_filter)
// di_length = input.int(14, 'DI周期', group = group_filter, minval = 1)
// adx_smoothing = input.int(14, 'ADX平滑周期', group = group_filter, minval = 1)
// adx_threshold = input.int(25, 'ADX强度阈值', group = group_filter, minval = 15, maxval = 50)

// // 仓位管理
// var group_position = '仓位管理'
// enable_atr_sizing = input.bool(true, '启用波动仓位管理', group = group_position)
// atr_period = input.int(14, 'ATR周期', group = group_position, minval = 5, maxval = 50)
// risk_percent = input.float(1.0, '风险百分比%', group = group_position, minval = 0.1, maxval = 100) / 100

// // 时间退出
// var group_time_exit = '时间退出'
// enable_time_exit = input.bool(true, '启用最大持仓时间', group = group_time_exit)
// max_holding_hours = input.int(24, '最大持仓时间(小时)', group = group_time_exit, minval = 1)

// // 时间设置
// var group_time = '回测时间'
// testStartYear = input.int(2025, '开始年份', group = group_time)
// testStartMonth = input.int(1, '开始月份', group = group_time)
// testStartDay = input.int(1, '开始日', group = group_time)
// testStopYear = input.int(2099, '结束年份', group = group_time)
// testStopMonth = input.int(12, '结束月份', group = group_time)
// testStopDay = input.int(31, '结束日', group = group_time)

// // 交易时段
// var group_trading_time = '交易时段'
// tradingtime = input.bool(true, '使用交易时段', group = group_trading_time)
// start_hour = input.int(8, '开始小时', group = group_trading_time)
// start_minute = input.int(0, '开始分钟', group = group_trading_time)
// end_hour = input.int(23, '结束小时', group = group_trading_time)
// end_minute = input.int(59, '结束分钟', group = group_trading_time)
// timezone_offset = input.int(8, '时区偏移', group = group_trading_time)

// // ======== 全局变量 ========
// varip float entry_price = na
// varip float highestHigh = na
// varip float lowestLow = na
// varip int daily_trades = 0
// varip float daily_equity_start = strategy.initial_capital
// varip string last_trade_day = na
// varip bool trading_paused = false
// varip int pause_start_time = 0
// varip float base_equity_after_stop = na
// varip float max_normal_equity = strategy.initial_capital
// varip bool recovery_mode = false
// varip float current_equity = na
// varip int current_time = na
// varip string current_exchange_day = na
// varip int entry_bar_time = na
// varip bool daily_loss_breached = false

// // 添加分批止盈状态变量
// varip bool exit1_triggered = false
// varip bool exit2_triggered = false
// varip bool exit3_triggered = false

// // ======== 核心逻辑 ========
// in_test_period() =>
//     time >= timestamp(testStartYear, testStartMonth, testStartDay, 0, 0) and time <= timestamp(testStopYear, testStopMonth, testStopDay, 23, 59)

// in_trading_time() =>
//     exchange_time = time + timezone_offset * 60 * 60 * 1000
//     exchange_hour = hour(exchange_time)
//     exchange_minute = minute(exchange_time)
//     time_ok = exchange_hour > start_hour or exchange_hour == start_hour and exchange_minute >= start_minute
//     time_ok := time_ok and (exchange_hour < end_hour or exchange_hour == end_hour and exchange_minute <= end_minute)
//     time_ok

// current_equity := strategy.equity
// current_time := time
// current_exchange_day := str.format('{0}-{1}-{2}', year(time + timezone_offset * 60 * 60 * 1000), month(time + timezone_offset * 60 * 60 * 1000), dayofmonth(time + timezone_offset * 60 * 60 * 1000))

// [dip, din, adx_value] = ta.dmi(di_length, adx_smoothing)
// valid_trend_long = not enable_adx_filter or adx_value >= adx_threshold and dip > din
// valid_trend_short = not enable_adx_filter or adx_value >= adx_threshold and din > dip

// atr_value = ta.atr(atr_period)
// position_size = enable_atr_sizing ? strategy.equity * risk_percent / (atr_value * syminfo.pointvalue) : strategy.equity / close

// if strategy.position_size == 0
//     entry_bar_time := na
//     entry_bar_time
// else if na(entry_bar_time)
//     entry_bar_time := current_time
//     entry_bar_time

// holding_hours = (current_time - entry_bar_time) / (1000 * 60 * 60)
// time_exit_condition = enable_time_exit and holding_hours >= max_holding_hours

// // 每日重置逻辑
// if current_exchange_day != last_trade_day
//     daily_trades := 0
//     daily_equity_start := strategy.equity
//     last_trade_day := current_exchange_day
//     trading_paused := false
//     daily_loss_breached := false
//     max_normal_equity := strategy.initial_capital
//     max_normal_equity

// // 每日亏损检测
// daily_loss = enable_daily_loss ? (daily_equity_start - current_equity) / daily_equity_start : 0.0
// daily_loss_breached := enable_daily_loss and daily_loss >= daily_max_loss_pct and daily_max_loss_pct > 0
// if daily_loss_breached
//     trading_paused := true
//     trading_paused

// // 回撤控制
// if enable_drawdown
//     if not recovery_mode
//         max_normal_equity := math.max(max_normal_equity, current_equity)
//         drawdown = (max_normal_equity - current_equity) / max_normal_equity
//         if drawdown >= max_drawdown_pct and strategy.position_size != 0
//             strategy.close_all()
//             base_equity_after_stop := current_equity
//             recovery_mode := true
//             trading_paused := true
//             pause_start_time := current_time
//             pause_start_time
//     if recovery_mode
//         recovery_return = (current_equity - base_equity_after_stop) / base_equity_after_stop
//         pause_duration = (current_time - pause_start_time) / (1000 * 60 * 60)
//         if recovery_return >= recovery_threshold_pct or pause_duration >= min_pause_hours
//             recovery_mode := false
//             trading_paused := false
//             max_normal_equity := current_equity
//             max_normal_equity

// can_trade = not trading_paused and (not enable_daily_limit or daily_trades < max_daily_trades)

// // 交易信号
// ma1 = ta.sma(close, ma1_period)
// ma2 = ta.sma(close, ma2_period)
// ma3 = ta.sma(close, ma3_period)
// long_entry = barstate.isconfirmed and close > ma1 and close > ma2 and close > ma3 and ma1 > ma2 and ma2 > ma3 and valid_trend_long
// short_entry = barstate.isconfirmed and close < ma1 and close < ma2 and close < ma3 and ma1 < ma2 and ma2 < ma3 and valid_trend_short
// close_long = barstate.isconfirmed and (close < ma1 or close < ma2 or close < ma3)
// close_short = barstate.isconfirmed and (close > ma1 or close > ma2 or close > ma3)

// // 执行交易
// if in_test_period() and can_trade and (not tradingtime or in_trading_time())
//     if strategy.position_size == 0
//         if is_long_allowed and long_entry
//             entry_price := close
//             highestHigh := high
//             // 重置分批止盈状态
//             exit1_triggered := false
//             exit2_triggered := false
//             exit3_triggered := false
            
//             strategy.entry('Long', strategy.long, qty = position_size, comment = '开多')
//             daily_trades := daily_trades + 1
//             if selected_stop_type == '固定止损'
//                 strategy.exit('Long Exit', 'Long', stop = entry_price * (1 - fixed_stop_loss_pct), limit = entry_price * (1 + fixed_take_profit_pct), comment = '固损')
//             else if selected_stop_type == '移动止损'
//                 strategy.exit('Long Trail', 'Long', stop = entry_price * (1 - initial_stop_loss_pct), comment = '移损')

//         else if is_short_allowed and short_entry
//             entry_price := close
//             lowestLow := low
//             // 重置分批止盈状态
//             exit1_triggered := false
//             exit2_triggered := false
//             exit3_triggered := false
            
//             strategy.entry('Short', strategy.short, qty = position_size, comment = '开空')
//             daily_trades := daily_trades + 1
//             if selected_stop_type == '固定止损'
//                 strategy.exit('Short Exit', 'Short', stop = entry_price * (1 + fixed_stop_loss_pct), limit = entry_price * (1 - fixed_take_profit_pct), comment = '固损')
//             else if selected_stop_type == '移动止损'
//                 strategy.exit('Short Trail', 'Short', stop = entry_price * (1 + initial_stop_loss_pct), comment = '移损')

//     // 更新跟踪止损
//     if strategy.position_size > 0 and not trading_paused  // 添加交易暂停检查
//         highestHigh := math.max(high, highestHigh)
//         if selected_stop_type == '移动止损'
//             trail_activate = highestHigh >= entry_price * (1 + trailing_start_pct)
//             stop_price = trail_activate ? highestHigh * (1 - trailing_stop_pct) : entry_price * (1 - initial_stop_loss_pct)
//             strategy.exit('Long Trail', 'Long', stop = stop_price, comment = '移损')
            
//         // 添加多头分批止盈逻辑
//         if enable_pyramid_exit
//             float unrealized_pct = (close - entry_price) / entry_price
//             float current_position = math.abs(strategy.position_size)
            
//             if not exit1_triggered and unrealized_pct >= exit1_profit
//                 float exit1_size = math.min(exit1_qty * current_position, current_position)
//                 strategy.order('Long Exit1', strategy.short, qty = exit1_size, comment = '一阶止盈')
//                 exit1_triggered := true
            
//             if not exit2_triggered and unrealized_pct >= exit2_profit
//                 float exit2_size = math.min(exit2_qty * current_position, current_position)
//                 strategy.order('Long Exit2', strategy.short, qty = exit2_size, comment = '二阶止盈')
//                 exit2_triggered := true
            
//             if not exit3_triggered and unrealized_pct >= exit3_profit
//                 strategy.close_all(comment = '三阶全平')
//                 exit3_triggered := true

//             if exit1_qty == 1.0 or exit2_qty == 1.0 or exit3_qty == 1.0
//                 strategy.close_all(comment = '全额止盈')
                
//     else if strategy.position_size < 0 and not trading_paused  // 添加交易暂停检查
//         lowestLow := math.min(low, lowestLow)
//         if selected_stop_type == '移动止损'
//             trail_activate = lowestLow <= entry_price * (1 - trailing_start_pct)
//             stop_price = trail_activate ? lowestLow * (1 + trailing_stop_pct) : entry_price * (1 + initial_stop_loss_pct)
//             strategy.exit('Short Trail', 'Short', stop = stop_price, comment = '移损')
            
//         // 添加空头分批止盈逻辑
//         if enable_pyramid_exit
//             float unrealized_pct_short = (entry_price - close) / entry_price
//             float current_position = math.abs(strategy.position_size)
            
//             if not exit1_triggered and unrealized_pct_short >= exit1_profit
//                 float exit1_size = math.min(exit1_qty * current_position, current_position)
//                 strategy.order('Short Exit1', strategy.long, qty = exit1_size, comment = '一阶止盈')
//                 exit1_triggered := true
            
//             if not exit2_triggered and unrealized_pct_short >= exit2_profit
//                 float exit2_size = math.min(exit2_qty * current_position, current_position)
//                 strategy.order('Short Exit2', strategy.long, qty = exit2_size, comment = '二阶止盈')
//                 exit2_triggered := true
            
//             if not exit3_triggered and unrealized_pct_short >= exit3_profit
//                 strategy.close_all(comment = '三阶全平')
//                 exit3_triggered := true

//             if exit1_qty == 1.0 or exit2_qty == 1.0 or exit3_qty == 1.0
//                 strategy.close_all(comment = '全额止盈')
    
//     // 平仓逻辑
//     if strategy.position_size > 0 and (close_long or time_exit_condition)
//         strategy.close('Long', comment = '平多')
//     if strategy.position_size < 0 and (close_short or time_exit_condition)
//         strategy.close('Short', comment = '平空')

// // 可视化
// plot(ma1, color = color.blue)
// plot(ma2, color = color.orange)
// plot(ma3, color = color.purple)

// //@version=6
// strategy('3MA Strategy v12', overlay = true, pyramiding = 1, initial_capital = 1000, default_qty_type = strategy.percent_of_equity, default_qty_value = 100, process_orders_on_close = true)

// // ======== 输入参数 ========
// // 交易方向
// trade_direction = input.string('both', '交易方向', options = ['long', 'short', 'both'])
// is_long_allowed = trade_direction == 'long' or trade_direction == 'both'
// is_short_allowed = trade_direction == 'short' or trade_direction == 'both'

// // 均线参数
// var group_ma = '均线参数'
// ma1_period = input.int(30, title = 'MA1周期', group = group_ma, minval = 1)
// ma2_period = input.int(45, title = 'MA2周期', group = group_ma, minval = 1)
// ma3_period = input.int(60, title = 'MA3周期', group = group_ma, minval = 1)

// // 止损参数
// var group_stop = '止损参数'
// selected_stop_type = input.string('固定止损', title = '止损类型', options = ['固定止损', '移动止损', '无'], group = group_stop)
// fixed_stop_loss_pct = input.float(1.0, '固定止损%', group = group_stop, minval = 0, maxval = 100, inline = 'fixed_stop') / 100
// fixed_take_profit_pct = input.float(300.0, '固定止盈%', group = group_stop, minval = 0, maxval = 2000, inline = 'fixed_stop') / 100
// trailing_start_pct = input.float(20.0, '移动止盈触发%', group = group_stop, minval = 0, maxval = 2000, inline = 'trailing_stop') / 100
// trailing_stop_pct = input.float(1.0, '移动止损回调%', group = group_stop, minval = 0, maxval = 100, inline = 'trailing_stop') / 100
// immediate_stop = input.bool(true, '使用开仓初始止损', group = group_stop)
// initial_stop_loss_pct = input.float(0.6, '初始止损%', group = group_stop, minval = 0, maxval = 100) / 100

// // 风险控制
// var group_risk = '风险控制'
// enable_daily_limit = input.bool(false, '启用每日交易限制', group = group_risk)
// max_daily_trades = input.int(5, '最大每日交易次数', group = group_risk, minval = 1)
// enable_drawdown = input.bool(true, '启用最大回撤控制', group = group_risk)
// max_drawdown_pct = input.float(4, '最大回撤%', group = group_risk, minval = 0, maxval = 100) / 100
// recovery_threshold_pct = input.float(2, '恢复阈值%', group = group_risk, minval = 0.1, maxval = 100) / 100
// enable_daily_loss = input.bool(false, '启用每日最大亏损', group = group_risk)
// daily_max_loss_pct = input.float(5, '每日最大亏损%', group = group_risk, minval = 0, maxval = 50) / 100
// min_pause_hours = input.int(12, '最小暂停时间(小时)', group = group_risk, minval = 1)

// // 趋势过滤
// var group_filter = '趋势过滤'
// enable_adx_filter = input.bool(true, '启用ADX趋势过滤', group = group_filter)
// di_length = input.int(14, 'DI周期', group = group_filter, minval = 1)
// adx_smoothing = input.int(14, 'ADX平滑周期', group = group_filter, minval = 1)
// adx_threshold = input.int(25, 'ADX强度阈值', group = group_filter, minval = 15, maxval = 50)

// // 仓位管理
// var group_position = '仓位管理'
// enable_atr_sizing = input.bool(true, '启用波动仓位管理', group = group_position)
// atr_period = input.int(14, 'ATR周期', group = group_position, minval = 5, maxval = 50)
// risk_percent = input.float(1.0, '风险百分比%', group = group_position, minval = 0.1, maxval = 100) / 100

// // 时间退出
// var group_time_exit = '时间退出'
// enable_time_exit = input.bool(true, '启用最大持仓时间', group = group_time_exit)
// max_holding_hours = input.int(24, '最大持仓时间(小时)', group = group_time_exit, minval = 1)

// // 时间设置
// var group_time = '回测时间'
// testStartYear = input.int(2025, '开始年份', group = group_time)
// testStartMonth = input.int(1, '开始月份', group = group_time)
// testStartDay = input.int(1, '开始日', group = group_time)
// testStopYear = input.int(2099, '结束年份', group = group_time)
// testStopMonth = input.int(12, '结束月份', group = group_time)
// testStopDay = input.int(31, '结束日', group = group_time)

// // 交易时段
// var group_trading_time = '交易时段'
// tradingtime = input.bool(true, '使用交易时段', group = group_trading_time)
// start_hour = input.int(8, '开始小时', group = group_trading_time)
// start_minute = input.int(0, '开始分钟', group = group_trading_time)
// end_hour = input.int(23, '结束小时', group = group_trading_time)
// end_minute = input.int(59, '结束分钟', group = group_trading_time)
// timezone_offset = input.int(8, '时区偏移', group = group_trading_time)

// // ======== 全局变量 ========
// varip float entry_price = na
// varip float highestHigh = na
// varip float lowestLow = na
// varip int daily_trades = 0
// varip float daily_equity_start = strategy.initial_capital
// varip string last_trade_day = na
// varip bool trading_paused = false
// varip int pause_start_time = 0
// varip float base_equity_after_stop = na
// varip float max_normal_equity = strategy.initial_capital
// varip bool recovery_mode = false
// varip float current_equity = na
// varip int current_time = na
// varip string current_exchange_day = na
// varip int entry_bar_time = na
// varip bool daily_loss_breached = false

// // ======== 核心逻辑 ========
// in_test_period() =>
//     time >= timestamp(testStartYear, testStartMonth, testStartDay, 0, 0) and time <= timestamp(testStopYear, testStopMonth, testStopDay, 23, 59)

// in_trading_time() =>
//     exchange_time = time + timezone_offset * 60 * 60 * 1000
//     exchange_hour = hour(exchange_time)
//     exchange_minute = minute(exchange_time)
//     time_ok = exchange_hour > start_hour or exchange_hour == start_hour and exchange_minute >= start_minute
//     time_ok := time_ok and (exchange_hour < end_hour or exchange_hour == end_hour and exchange_minute <= end_minute)
//     time_ok

// current_equity := strategy.equity
// current_time := time
// current_exchange_day := str.format('{0}-{1}-{2}', year(time + timezone_offset * 60 * 60 * 1000), month(time + timezone_offset * 60 * 60 * 1000), dayofmonth(time + timezone_offset * 60 * 60 * 1000))

// [dip, din, adx_value] = ta.dmi(di_length, adx_smoothing)
// valid_trend_long = not enable_adx_filter or adx_value >= adx_threshold and dip > din
// valid_trend_short = not enable_adx_filter or adx_value >= adx_threshold and din > dip

// atr_value = ta.atr(atr_period)
// position_size = enable_atr_sizing ? strategy.equity * risk_percent / (atr_value * syminfo.pointvalue) : strategy.equity / close

// if strategy.position_size == 0
//     entry_bar_time := na
//     entry_bar_time
// else if na(entry_bar_time)
//     entry_bar_time := current_time
//     entry_bar_time

// holding_hours = (current_time - entry_bar_time) / (1000 * 60 * 60)
// time_exit_condition = enable_time_exit and holding_hours >= max_holding_hours

// // 每日重置逻辑
// if current_exchange_day != last_trade_day
//     daily_trades := 0
//     daily_equity_start := strategy.equity
//     last_trade_day := current_exchange_day
//     trading_paused := false
//     daily_loss_breached := false
//     max_normal_equity := strategy.initial_capital
//     max_normal_equity

// // 每日亏损检测
// daily_loss = enable_daily_loss ? (daily_equity_start - current_equity) / daily_equity_start : 0.0
// daily_loss_breached := enable_daily_loss and daily_loss >= daily_max_loss_pct and daily_max_loss_pct > 0
// if daily_loss_breached
//     trading_paused := true
//     trading_paused

// // 回撤控制
// if enable_drawdown
//     if not recovery_mode
//         max_normal_equity := math.max(max_normal_equity, current_equity)
//         drawdown = (max_normal_equity - current_equity) / max_normal_equity
//         if drawdown >= max_drawdown_pct and strategy.position_size != 0
//             strategy.close_all()
//             base_equity_after_stop := current_equity
//             recovery_mode := true
//             trading_paused := true
//             pause_start_time := current_time
//             pause_start_time
//     if recovery_mode
//         recovery_return = (current_equity - base_equity_after_stop) / base_equity_after_stop
//         pause_duration = (current_time - pause_start_time) / (1000 * 60 * 60)
//         if recovery_return >= recovery_threshold_pct or pause_duration >= min_pause_hours
//             recovery_mode := false
//             trading_paused := false
//             max_normal_equity := current_equity
//             max_normal_equity

// can_trade = not trading_paused and (not enable_daily_limit or daily_trades < max_daily_trades)

// // 交易信号
// ma1 = ta.sma(close, ma1_period)
// ma2 = ta.sma(close, ma2_period)
// ma3 = ta.sma(close, ma3_period)
// long_entry = barstate.isconfirmed and close > ma1 and close > ma2 and close > ma3 and ma1 > ma2 and ma2 > ma3 and valid_trend_long
// short_entry = barstate.isconfirmed and close < ma1 and close < ma2 and close < ma3 and ma1 < ma2 and ma2 < ma3 and valid_trend_short
// close_long = barstate.isconfirmed and (close < ma1 or close < ma2 or close < ma3)
// close_short = barstate.isconfirmed and (close > ma1 or close > ma2 or close > ma3)

// // 执行交易
// if in_test_period() and can_trade and (not tradingtime or in_trading_time())
//     if strategy.position_size == 0
//         if is_long_allowed and long_entry
//             entry_price := close
//             highestHigh := high
//             strategy.entry('Long', strategy.long, qty = position_size, comment = '开多')
//             daily_trades := daily_trades + 1
//             if selected_stop_type == '固定止损'
//                 strategy.exit('Long Exit', 'Long', stop = entry_price * (1 - fixed_stop_loss_pct), limit = entry_price * (1 + fixed_take_profit_pct), comment = '固损')
//             else if selected_stop_type == '移动止损'
//                 strategy.exit('Long Trail', 'Long', stop = entry_price * (1 - initial_stop_loss_pct), comment = '移损')  // 移除 immediate_stop 条件

//         else if is_short_allowed and short_entry
//             entry_price := close
//             lowestLow := low
//             strategy.entry('Short', strategy.short, qty = position_size, comment = '开空')
//             daily_trades := daily_trades + 1
//             if selected_stop_type == '固定止损'
//                 strategy.exit('Short Exit', 'Short', stop = entry_price * (1 + fixed_stop_loss_pct), limit = entry_price * (1 - fixed_take_profit_pct), comment = '固损')
//             else if selected_stop_type == '移动止损'
//                 strategy.exit('Short Trail', 'Short', stop = entry_price * (1 + initial_stop_loss_pct), comment = '移损')  // 移除 immediate_stop 条件

//     // 更新跟踪止损
//     if strategy.position_size > 0 and not trading_paused  // 添加交易暂停检查
//         highestHigh := math.max(high, highestHigh)
//         if selected_stop_type == '移动止损'
//             trail_activate = highestHigh >= entry_price * (1 + trailing_start_pct)
//             stop_price = trail_activate ? highestHigh * (1 - trailing_stop_pct) : entry_price * (1 - initial_stop_loss_pct)
//             strategy.exit('Long Trail', 'Long', stop = stop_price, comment = '移损')
//     else if strategy.position_size < 0 and not trading_paused  // 添加交易暂停检查
//         lowestLow := math.min(low, lowestLow)
//         if selected_stop_type == '移动止损'
//             trail_activate = lowestLow <= entry_price * (1 - trailing_start_pct)
//             stop_price = trail_activate ? lowestLow * (1 + trailing_stop_pct) : entry_price * (1 + initial_stop_loss_pct)
//             strategy.exit('Short Trail', 'Short', stop = stop_price, comment = '移损')
//     // 平仓逻辑
//     if strategy.position_size > 0 and (close_long or time_exit_condition)
//         strategy.close('Long', comment = '平多')
//     if strategy.position_size < 0 and (close_short or time_exit_condition)
//         strategy.close('Short', comment = '平空')

// // 可视化
// plot(ma1, color = color.blue)
// plot(ma2, color = color.orange)
// plot(ma3, color = color.purple)