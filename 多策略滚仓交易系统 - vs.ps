//@version=6
strategy(title="多策略滚仓交易系统", overlay=true, pyramiding=10, default_qty_type=strategy.percent_of_equity, commission_type=strategy.commission.percent, commission_value=0.04)

// ===== 策略参数配置 =====
// 基础参数
tradeDirection = input.string("多空双向", options=["仅多", "仅空", "多空双向"], title="交易方向")
strategyChoice = input.string("雨后的夏天", options=["雨后的夏天", "午饭浮云", "李法师Tony", "肥宅", "DOGE维持", "FIL浮盈"], title="选择策略")
pyramidLevels = input.int(12, "最大加仓次数", minval=1, maxval=12)
exchangeLeverage = input.float(100, "交易所杠杆", step=5, minval=1, maxval=1000)
confirmBars = input.int(1, "信号确认K线数", minval=1, maxval=4)

// 风险控制参数
enableStopLoss = input.bool(true, "启用动态止损")
stopLossPerc = input.float(2.0, "止损百分比", step=0.1, minval=0.1, maxval=50)
enableTakeProfit = input.bool(false, "启用止盈")
takeProfitPerc = input.float(100.0, "止盈百分比", step=5, minval=10)

// 高级参数
initialCapital = input.float(800, "初始资金(U)", minval=10, step=100)
priceSource = input.source(close, "价格源选择")

// 资金曲线
var float currentEquity = initialCapital
currentEquity := math.max(strategy.equity, initialCapital)  // 使用内置变量动态更新

// ===== 核心计算模块 =====
// 均线系统
ma30 = ta.sma(close, 30)
ma45 = ta.sma(close, 45)
ma60 = ta.sma(close, 60)

// 信号生成系统
checkLongCondition() =>
    crossOver = close > ma30 and close > ma45 and close > ma60
    confirmation = ta.barssince(crossOver) < confirmBars
    strategy.position_size == 0 ? crossOver and confirmation : false

checkShortCondition() =>
    crossUnder = close < ma30 and close < ma45 and close < ma60
    confirmation = ta.barssince(crossUnder) < confirmBars
    strategy.position_size == 0 ? crossUnder and confirmation : false

// 动态仓位计算（集成所有策略）
calculatePositionSize() =>
    currentEquity = strategy.equity
    posSize = 0.0
    
    switch strategyChoice
        "雨后的夏天"=>
            riskPercent = math.pow(0.5, strategy.opentrades) * 100
            posSize := (currentEquity * riskPercent / 100) * exchangeLeverage / close
        
        "午饭浮云"=>
            basePercent = (strategy.opentrades == 0 ? 10 : 20)
            posSize := (currentEquity * basePercent / 100) * exchangeLeverage / close
        
        "李法师Tony"=>
            basePercent = 10
            posSize := (initialCapital * basePercent / 100) * exchangeLeverage / close
        
        "肥宅"=>
            fixedPercent = 2
            posSize := (currentEquity * fixedPercent / 100) * exchangeLeverage / close
        
        "DOGE维持"=>
            posSize := (currentEquity * 25 / 100) * exchangeLeverage / close
        
        "FIL浮盈"=>
            decayFactor = 1.0 / math.sqrt(strategy.opentrades + 1)
            posSize := (currentEquity * 25 * decayFactor / 100) * exchangeLeverage / close
    
    math.floor(posSize * 100000) / 100000 // 精度控制

// 动态杠杆监控
leverageMonitor = math.abs(strategy.position_size * close / strategy.equity) * exchangeLeverage

// ===== 交易执行模块 =====
// 开仓逻辑
if checkLongCondition() and (tradeDirection == "仅多" or tradeDirection == "多空双向")
    strategy.entry("Long", strategy.long, qty=calculatePositionSize())

if checkShortCondition() and (tradeDirection == "仅空" or tradeDirection == "多空双向")
    strategy.entry("Short", strategy.short, qty=calculatePositionSize())

// 金字塔加仓系统
var int pyramidCounter = 0
if strategy.opentrades > pyramidCounter and strategy.opentrades < pyramidLevels
    pyramidCounter := strategy.opentrades
    strategy.entry("Pyramid_" + str.tostring(pyramidCounter), 
                  strategy.position_size > 0 ? strategy.long : strategy.short, 
                  qty=calculatePositionSize() * math.pow(2, pyramidCounter))

// ===== 风险控制系统 =====
// 动态止损
var float dynamicStop = 0.0
if enableStopLoss and strategy.position_size != 0
    stopPrice = strategy.position_avg_price * (1 - stopLossPerc / 100 * (strategy.position_size > 0 ? 1 : -1))
    dynamicStop := strategy.position_size > 0 ? math.max(dynamicStop, stopPrice) : math.min(dynamicStop, stopPrice)
    strategy.exit("StopLoss", "Long", stop=dynamicStop)
    strategy.exit("StopLoss", "Short", stop=dynamicStop)

// 止盈系统
if enableTakeProfit and strategy.position_size != 0
    takeProfitPrice = strategy.position_avg_price * (1 + takeProfitPerc / 100 * (strategy.position_size > 0 ? 1 : -1))
    strategy.exit("TakeProfit", "Long", limit=takeProfitPrice)
    strategy.exit("TakeProfit", "Short", limit=takeProfitPrice)

// ===== 可视化系统 =====
// 均线显示
plot(ma30, color=color.new(color.blue, 0), title="30MA")
plot(ma45, color=color.new(color.orange, 0), title="45MA")
plot(ma60, color=color.new(color.purple, 0), title="60MA")

// 仓位标记
plotshape(series=checkLongCondition(), style=shape.triangleup, location=location.belowbar, color=color.green, size=size.small)
plotshape(series=checkShortCondition(), style=shape.triangledown, location=location.abovebar, color=color.red, size=size.small)

// 杠杆监控标签
var label levLabel = label.new(x=bar_index, y=close, style=label.style_label_left, color=color.new(#2c3e50, 90))
label.set_xy(levLabel, bar_index, close)
label.set_text(levLabel, str.tostring(leverageMonitor, "#.##") + "X Leverage")
label.set_color(levLabel, leverageMonitor > 50 ? color.red : leverageMonitor > 20 ? color.orange : color.green)

// 绩效分析
var table perfTable = table.new(position.bottom_right, 4, 4)
table.cell(perfTable, 0, 0, "最大回撤")
table.cell(perfTable, 1, 0, str.tostring(strategy.max_drawdown, "#.##%"))
table.cell(perfTable, 0, 1, "胜率")
table.cell(perfTable, 1, 1, str.tostring(strategy.wintrades/strategy.closedtrades*100, "#.##%"))
table.cell(perfTable, 0, 2, "盈亏比")
table.cell(perfTable, 1, 2, str.tostring(strategy.grossprofit/strategy.grossloss, "#.##"))
