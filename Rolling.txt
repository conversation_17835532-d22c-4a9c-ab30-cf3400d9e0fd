//@version=6
strategy("FeiZhai Rolling Strategy", overlay=true, pyramiding=5, initial_capital=10000, default_qty_type=strategy.percent_of_equity, default_qty_value=100, commission_type=strategy.commission.percent, commission_value=0.1)

// 用户可调参数
input_useBreakout = input.bool(true, "启用突破加仓模式")
input_usePullback = input.bool(true, "启用回调加仓模式")
input_atrLength = input.int(14, "ATR周期", minval=5)
input_maLength = input.int(30, "均线周期", minval=10)
input_riskPercent = input.float(1.0, "单笔风险百分比", minval=0.1, maxval=5)
input_leverage = input.int(5, "杠杆倍数", minval=1, maxval=100)
input_backtestStart = input.time(timestamp("2020-01-01"), "回测起始时间")

// 指标计算
atr = ta.atr(input_atrLength)
ma = ta.sma(close, input_maLength)
upperBand = ta.highest(high, 20)
lowerBand = ta.lowest(low, 20)

// 突破条件判断
doubleBottom = ta.lowest(low, 10)[2] == ta.lowest(low, 10)[1] and low > ta.lowest(low, 10)[1]
breakoutSignal = ta.crossover(close, upperBand)
triangleBreak = high < high[1] and low > low[1] // 简化版三角形突破检测

// 回调条件判断
pullbackToMA = ta.crossunder(low, ma) and close > ma
demandZone = low > ta.lowest(low, 20) * 1.02 // 简化版需求区检测

// 仓位管理
positionSize = strategy.equity * input_riskPercent / 100 * input_leverage
var float baseStop = na
var int positionTier = 0

// 策略逻辑
if time >= input_backtestStart
    // 初始入场
    if (strategy.position_size == 0) and (breakoutSignal or doubleBottom)
        strategy.entry("BaseEntry", strategy.long, qty=positionSize)
        baseStop := low - atr * 1.5
        positionTier := 1
  
    // 突破加仓模式
    if input_useBreakout and strategy.position_size > 0
        if triangleBreak and positionTier < 5
            strategy.entry("BreakoutAdd", strategy.long, qty=positionSize)
            strategy.exit("BreakoutExit", "BreakoutAdd", stop=baseStop, trail_points=close*0.05)
            positionTier += 1
  
    // 回调加仓模式
    if input_usePullback and strategy.position_size > 0
        if (pullbackToMA or demandZone) and (close - baseStop) > (baseStop * 0.03)
            strategy.entry("PullbackAdd", strategy.long, qty=positionSize)
            strategy.exit("PullbackExit", "PullbackAdd", trail_points=close*0.03)
            positionTier += 1
  
    // 移动止损管理
    if strategy.position_size > 0
        trailStop = close - atr * 1.2
        baseStop := math.max(baseStop, trailStop)
        strategy.exit("BaseExit", "BaseEntry", stop=baseStop)

// 可视化
plot(ma, "MA", color=color.blue)
plot(upperBand, "Upper Band", color=color.gray)
plot(lowerBand, "Lower Band", color=color.gray)
hline(strategy.position_avg_price, "Avg Price", color=color.purple)