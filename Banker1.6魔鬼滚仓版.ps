//@version=6
strategy('Banker1.6魔鬼滚仓版', overlay = true, pyramiding = 10, initial_capital = 1000, default_qty_type = strategy.fixed, default_qty_value = 1, commission_type = strategy.commission.percent, commission_value = 0.07)

// ======== 主体策略参数（保持不变）========
trade_direction = input.string('both', '交易方向', options = ['long', 'short', 'both'])
is_long_allowed = trade_direction == 'long' or trade_direction == 'both'
is_short_allowed = trade_direction == 'short' or trade_direction == 'both'

// ======== 🔥 魔鬼滚仓风控系统 ========
group_devil_risk = "🔥 魔鬼滚仓风控系统"
initial_risk_percent = input.float(5.0, '首单风险比例[%]', step = 0.5, minval = 1.0, maxval = 10.0, group = group_devil_risk)
base_stop_percent = input.float(3.0, '基础止损[%]', step = 0.1, minval = 1.0, maxval = 10.0, group = group_devil_risk)
enable_rolling = input.bool(true, '启用滚仓模式', group = group_devil_risk)
max_pyramid_levels = input.int(5, '最大加仓次数', minval = 1, maxval = 10, group = group_devil_risk)

// ======== 📈 动态止盈系统 ========
group_dynamic_tp = "📈 动态止盈系统"
trailing_profit_step = input.float(10.0, '止盈步长[%]', step = 1.0, minval = 5.0, maxval = 20.0, group = group_dynamic_tp)
trailing_stop_ratio = input.float(0.7, '止损跟进比例', step = 0.1, minval = 0.5, maxval = 0.9, group = group_dynamic_tp)
extreme_market_step = input.float(5.0, '极端行情步长[%]', step = 1.0, minval = 3.0, maxval = 10.0, group = group_dynamic_tp)
extreme_market_ratio = input.float(0.8, '极端行情跟进比例', step = 0.1, minval = 0.6, maxval = 0.9, group = group_dynamic_tp)

// ======== ⚡ 加仓触发系统 ========
group_add_position = "⚡ 加仓触发系统"
profit_threshold_1 = input.float(20.0, '第一档盈利阈值[%]', step = 5.0, minval = 10.0, maxval = 50.0, group = group_add_position)
profit_threshold_2 = input.float(50.0, '第二档盈利阈值[%]', step = 5.0, minval = 30.0, maxval = 100.0, group = group_add_position)
profit_threshold_3 = input.float(100.0, '雪崩模式阈值[%]', step = 10.0, minval = 80.0, maxval = 200.0, group = group_add_position)
consecutive_wins_trigger = input.int(3, '连胜触发次数', minval = 2, maxval = 5, group = group_add_position)
volatility_threshold = input.float(15.0, '波动率阈值[%]', step = 1.0, minval = 10.0, maxval = 30.0, group = group_add_position)

// ======== 💰 物理隔离风控 ========
group_isolation = "💰 物理隔离风控"
enable_withdrawal_trigger = input.bool(true, '启用提现熔断', group = group_isolation)
daily_profit_limit = input.float(30.0, '当日盈利提现阈值[%]', step = 5.0, minval = 20.0, maxval = 100.0, group = group_isolation)
enable_freeze_mechanism = input.bool(true, '启用自毁机制', group = group_isolation)
max_consecutive_losses = input.int(3, '连败冻结次数', minval = 2, maxval = 5, group = group_isolation)
freeze_hours = input.int(24, '冻结时长[小时]', minval = 12, maxval = 72, group = group_isolation)

// ======== ⏰ 时间风控系统 ========
group_time_control = "⏰ 时间风控系统"
enable_golden_time = input.bool(true, '启用黄金时段限制', group = group_time_control)
golden_start_hour = input.int(9, '黄金时段开始时间', minval = 0, maxval = 23, group = group_time_control)
golden_end_hour = input.int(11, '黄金时段结束时间', minval = 0, maxval = 23, group = group_time_control)
enable_us_session = input.bool(true, '启用美股时段', group = group_time_control)
enable_death_time = input.bool(true, '启用死亡时间限制', group = group_time_control)
avoid_friday_night = input.bool(true, '避开周五晚间', group = group_time_control)

// ======== 🎯 传统风控保留 ========
group_legacy_risk = "🎯 传统风控保留"
enable_daily_limit = input.bool(false, '启用每日交易限制', group = group_legacy_risk)
max_daily_trades = input.int(20, '最大每日交易次数', group = group_legacy_risk, minval = 1)
enable_time_exit = input.bool(false, '启用最大持仓时间', group = group_legacy_risk)
max_holding_hours = input.int(48, '最大持仓时间(小时)', group = group_legacy_risk, minval = 1)

// 传统风控参数（简化版，魔鬼滚仓已有更强风控）
enable_daily_loss = input.bool(false, '启用每日亏损限制', group = group_legacy_risk)
daily_max_loss_pct = input.float(0.1, '每日最大亏损比例', step = 0.01, minval = 0.05, maxval = 0.5, group = group_legacy_risk)
enable_drawdown = input.bool(false, '启用回撤控制', group = group_legacy_risk)
max_drawdown_pct = input.float(0.15, '最大回撤比例', step = 0.01, minval = 0.05, maxval = 0.5, group = group_legacy_risk)
recovery_threshold_pct = input.float(0.02, '恢复阈值比例', step = 0.01, minval = 0.01, maxval = 0.1, group = group_legacy_risk)
min_pause_hours = input.int(8, '最小暂停时间(小时)', minval = 1, maxval = 48, group = group_legacy_risk)

// 回测时间
var group_time = '回测时间'
testStartYear = input.int(2025, '开始年份', group = group_time)
testStartMonth = input.int(3, '开始月份', group = group_time)
testStartDay = input.int(1, '开始日', group = group_time)
testStopYear = input.int(2099, '结束年份', group = group_time)
testStopMonth = input.int(12, '结束月份', group = group_time)
testStopDay = input.int(31, '结束日', group = group_time)

// ======== 魔鬼滚仓核心变量 ========
var float initial_capital = strategy.initial_capital
var float commission = 0.07

// 滚仓状态变量
var float total_profit = 0.0
var float base_entry_price = 0.0
var float dynamic_stop_price = 0.0
var int consecutive_wins = 0
var int consecutive_losses = 0
var bool freeze_trading = false
var int freeze_start_time = 0
var float daily_profit = 0.0
var int last_trade_day = 0
var float max_profit_today = 0.0

// 传统变量保留
varip float entry_price = na
varip float highestHigh = na
varip float lowestLow = na
varip int daily_trades = 0
varip float daily_equity_start = strategy.initial_capital
varip string last_trade_day_str = na
varip bool trading_paused = false
varip int pause_start_time = 0
varip float base_equity_after_stop = na
varip float max_normal_equity = strategy.initial_capital
varip bool recovery_mode = false
varip float current_equity = na
varip int current_time = na
varip string current_exchange_day = na
varip int entry_bar_time = na
varip bool daily_loss_breached = false

// 当前持仓信息
current_position_size = strategy.position_size
avg_entry_price = strategy.position_avg_price
current_profit_percent = current_position_size != 0 ? math.abs((close - avg_entry_price) / avg_entry_price * 100) : 0.0
current_profit_amount = current_position_size * (close - avg_entry_price)

// 波动率和市场状态
atr_period = 14
current_atr = ta.atr(atr_period)
price_volatility = current_atr / close * 100
trend_strength = math.abs(ta.change(close, 5)) / close * 100
is_extreme_market = price_volatility > volatility_threshold or trend_strength > 10.0

// ======== 魔鬼滚仓核心计算函数 ========

// 动态止损计算
calculate_dynamic_stop() =>
    if current_position_size != 0 and avg_entry_price > 0
        base_stop = current_position_size > 0 ?
                   avg_entry_price * (1 - base_stop_percent / 100) :
                   avg_entry_price * (1 + base_stop_percent / 100)

        // 根据盈利情况调整止损
        if current_profit_percent > 0
            profit_steps = math.floor(current_profit_percent / (is_extreme_market ? extreme_market_step : trailing_profit_step))
            stop_adjustment = profit_steps * (is_extreme_market ? extreme_market_ratio : trailing_stop_ratio) * (is_extreme_market ? extreme_market_step : trailing_profit_step) / 100

            if current_position_size > 0
                adjusted_stop = avg_entry_price * (1 + stop_adjustment)
                math.max(base_stop, adjusted_stop)
            else
                adjusted_stop = avg_entry_price * (1 - stop_adjustment)
                math.min(base_stop, adjusted_stop)
        else
            base_stop
    else
        0.0

dynamic_stop_price := calculate_dynamic_stop()

// 仓位大小计算
calculate_position_size(profit_level) =>
    base_size = strategy.equity * initial_risk_percent / 100 / close

    if not enable_rolling
        base_size
    else
        switch profit_level
            1 => base_size * 1.5  // 20%盈利档位
            2 => base_size * 2.0  // 50%盈利档位
            3 => base_size * 3.0  // 100%盈利档位（雪崩模式）
            => base_size

// 交易时段（保留原有逻辑）
var group_trading_time = '交易时段'
tradingtime = input.bool(true, '启用交易时段', group = group_trading_time)
asia_session = input.bool(true, '亚洲时段 09:00-13:00', group = group_trading_time)
london_session = input.bool(true, '伦敦时段 15:00-18:00', group = group_trading_time)
us_am_session = input.bool(true, '美国早盘 21:30-00:00', group = group_trading_time)
us_pm_session = input.bool(true, '美国下午 02:30-05:00', group = group_trading_time)
custom_session = input.bool(false, '启用自定义时段', group = group_trading_time)

// 自定义时段参数（仅在启用时显示）
custom_start_h = input.int(7, '开始小时', group = group_trading_time, minval=0, maxval=23, tooltip='仅在启用自定义时段时有效')
custom_start_m = input.int(0, '开始分钟', group = group_trading_time, minval=0, maxval=59)
custom_end_h = input.int(9, '结束小时', group = group_trading_time, minval=0, maxval=24)
custom_end_m = input.int(0, '结束分钟', group = group_trading_time, minval=0, maxval=59)
timezone_offset = input.int(8, '时区偏移', group = group_trading_time)

// ======== 时间判断函数 ========
in_session(s_hour, s_minute, e_hour, e_minute) =>
    exchange_time = time + timezone_offset * 60 * 60 * 1000
    exchange_h = hour(exchange_time)
    exchange_m = minute(exchange_time)

    // 处理跨日情况
    start_day_offset = s_hour < e_hour ? 0 : 1
    end_day_offset = s_hour < e_hour ? 0 : 1
    (exchange_h > s_hour or (exchange_h == s_hour and exchange_m >= s_minute)) and (exchange_h < e_hour or (exchange_h == e_hour and exchange_m <= e_minute))

// ======== 核心逻辑 ========
in_test_period() =>
    time >= timestamp(testStartYear, testStartMonth, testStartDay, 0, 0) and time <= timestamp(testStopYear, testStopMonth, testStopDay, 23, 59)

// 夏令时判断函数
is_dst() =>
    year_var = year(time)
    // 3月的第二个星期日（夏令时开始）
    march_date = timestamp(year_var, 3, 8) // 从3月8日开始找
    march_day = dayofweek(march_date) == dayofweek.sunday ? march_date : march_date + (7 - dayofweek(march_date) + 1) * 86400000
    dst_start = march_day + (7 * 86400000) // 加7天得到第二个星期日

    // 11月的第一个星期日（夏令时结束）
    november_date = timestamp(year_var, 11, 1)
    november_day = dayofweek(november_date) == dayofweek.sunday ? november_date : november_date + (7 - dayofweek(november_date) + 1) * 86400000
    dst_end = november_day

    time >= dst_start and time < dst_end

// 修改美国时段判断逻辑
get_us_session() =>
    var int us_am_start_h = is_dst() ? 21 : 22  // 北京时间
    var int us_am_start_m = is_dst() ? 30 : 30
    var int us_am_end_h = is_dst() ? 4 : 5      // 次日凌晨
    var int us_pm_start_h = is_dst() ? 2 : 3    // 美国下午时段对应北京时间
    var int us_pm_start_m = 30
    var int us_pm_end_h = is_dst() ? 5 : 6

    // 美国早盘时段（包含跨日判断）
    us_am = us_am_session and (in_session(us_am_start_h, us_am_start_m, 24, 0) or in_session(0, 0, us_am_end_h, 0))

    // 美国下午时段（次日凌晨）
    us_pm = us_pm_session and in_session(us_pm_start_h, us_pm_start_m, us_pm_end_h, 0)

    us_am or us_pm

// 修改后的交易时段判断
in_trading_time() =>
    if not tradingtime
        true
    else
        asia = asia_session and in_session(9, 0, 13, 0)
        london = london_session and in_session(15, 0, 18, 0)
        us = get_us_session()  // 调用新的美国时段判断
        custom = custom_session and in_session(custom_start_h, custom_start_m, custom_end_h, custom_end_m)

        asia or london or us or custom

// ======== 魔鬼滚仓风控条件 ========
// 时间风控 - 检查是否在交易禁忌时间
current_hour = hour(time, "UTC+8")
current_day = dayofweek(time)
is_golden_time = enable_golden_time ? (current_hour >= golden_start_hour and current_hour <= golden_end_hour) : true
is_death_time = enable_death_time and avoid_friday_night and current_day == dayofweek.friday and current_hour >= 18

// 每日盈利重置
current_day_int = dayofmonth(time)
if current_day_int != last_trade_day
    daily_profit := 0.0
    max_profit_today := 0.0
    last_trade_day := current_day_int

// 连胜连败统计更新
if strategy.closedtrades > strategy.closedtrades[1]
    last_trade_profit = strategy.closedtrades.profit(strategy.closedtrades - 1)
    if last_trade_profit > 0
        consecutive_wins := consecutive_wins + 1
        consecutive_losses := 0
    else
        consecutive_losses := consecutive_losses + 1
        consecutive_wins := 0

// 自毁机制 - 连续止损冻结
if enable_freeze_mechanism and consecutive_losses >= max_consecutive_losses and not freeze_trading
    freeze_trading := true
    freeze_start_time := time

// 解除冻结检查
if freeze_trading and (time - freeze_start_time) >= (freeze_hours * 3600000)  // 转换为毫秒
    freeze_trading := false
    consecutive_losses := 0

// 当日盈利监控
daily_profit := strategy.equity - strategy.initial_capital
daily_profit_percent = daily_profit / strategy.initial_capital * 100
max_profit_today := math.max(max_profit_today, daily_profit_percent)

// 提现熔断机制
should_withdraw = enable_withdrawal_trigger and daily_profit_percent > daily_profit_limit

// 加仓触发条件判断
can_add_position = strategy.opentrades < max_pyramid_levels and enable_rolling and not freeze_trading

// 盈利档位判断
profit_level_1_trigger = current_profit_percent >= profit_threshold_1 and consecutive_wins >= consecutive_wins_trigger
profit_level_2_trigger = current_profit_percent >= profit_threshold_2 and price_volatility > volatility_threshold
profit_level_3_trigger = current_profit_percent >= profit_threshold_3 and is_extreme_market

// 最终交易条件
can_open_trade = not freeze_trading and is_golden_time and not is_death_time and not should_withdraw
can_add_long_1 = profit_level_1_trigger and can_add_position and current_position_size > 0
can_add_long_2 = profit_level_2_trigger and can_add_position and current_position_size > 0
can_add_long_3 = profit_level_3_trigger and can_add_position and current_position_size > 0
can_add_short_1 = profit_level_1_trigger and can_add_position and current_position_size < 0
can_add_short_2 = profit_level_2_trigger and can_add_position and current_position_size < 0
can_add_short_3 = profit_level_3_trigger and can_add_position and current_position_size < 0

// 止损条件
should_stop_loss = current_position_size != 0 and
                  ((current_position_size > 0 and close <= dynamic_stop_price) or
                   (current_position_size < 0 and close >= dynamic_stop_price))

can_trade = not trading_paused and (not enable_daily_limit or daily_trades < max_daily_trades) and can_open_trade

// 每日重置逻辑（保留原有逻辑）
current_exchange_day := str.tostring(year) + '-' + str.tostring(month) + '-' + str.tostring(dayofmonth)
if current_exchange_day != last_trade_day_str
    daily_trades := 0
    daily_equity_start := strategy.equity
    last_trade_day_str := current_exchange_day
    trading_paused := false
    daily_loss_breached := false
    max_normal_equity := strategy.initial_capital
    max_normal_equity

// 每日亏损检测
daily_loss = enable_daily_loss ? (daily_equity_start - strategy.equity) / daily_equity_start : 0.0
daily_loss_breached := enable_daily_loss and daily_loss >= daily_max_loss_pct and daily_max_loss_pct > 0
if daily_loss_breached
    trading_paused := true
    trading_paused

// 回撤控制
if enable_drawdown
    if not recovery_mode
        max_normal_equity := math.max(max_normal_equity, strategy.equity)
        drawdown = (max_normal_equity - strategy.equity) / max_normal_equity
        if drawdown >= max_drawdown_pct and strategy.position_size != 0
            strategy.close_all()
            base_equity_after_stop := strategy.equity
            recovery_mode := true
            trading_paused := true
            pause_start_time := timenow
            pause_start_time
    if recovery_mode
        recovery_return = (strategy.equity - base_equity_after_stop) / base_equity_after_stop
        pause_duration = (timenow - pause_start_time) / (1000 * 60 * 60)
        if recovery_return >= recovery_threshold_pct or pause_duration >= min_pause_hours
            recovery_mode := false
            trading_paused := false
            max_normal_equity := strategy.equity
            max_normal_equity

// 主体逻辑：Banker策略信号
//functions
xrf(values, length) =>
    r_val = float(na)
    if length >= 1
        for i = 0 to length by 1
            if na(r_val) or not na(values[i])
                r_val := values[i]
                r_val
    r_val

xsa(src, len, wei) =>
    sumf = 0.0
    ma = 0.0
    out = 0.0
    sumf := nz(sumf[1]) - nz(src[len]) + src
    ma := na(src[len]) ? na : sumf / len
    out := na(out[1]) ? ma : (src * wei + out[1] * (len - wei)) / len
    out

//set up a simple model of banker fund flow trend
fundtrend = (3 * xsa((close - ta.lowest(low, 27)) / (ta.highest(high, 27) - ta.lowest(low, 27)) * 100, 5, 1) - 2 * xsa(xsa((close - ta.lowest(low, 27)) / (ta.highest(high, 27) - ta.lowest(low, 27)) * 100, 5, 1), 3, 1) - 50) * 1.032 + 50
//define typical price for banker fund
typ = (2 * close + high + low + open) / 5
//lowest low with mid term fib # 34
lol = ta.lowest(low, 34)
//highest high with mid term fib # 34
hoh = ta.highest(high, 34)
//define banker fund flow bull bear line
bullbearline = ta.ema((typ - lol) / (hoh - lol) * 100, 13)
//define banker entry signal
bankerentry = ta.crossover(fundtrend, bullbearline) and bullbearline < 25

// 定义蜡烛颜色和价格
var bool yellowcandle = false
var float Yellow_highprice = na
var float Yellow_closeprice = na
var float Yellow_lowprice = na
if bankerentry
    yellowcandle := true
    Yellow_highprice := high
    Yellow_closeprice := close
    Yellow_lowprice := low
    Yellow_lowprice

var bool whitecandle = false
var float White_highprice = na
var float White_closeprice = na
var float White_lowprice = na
if fundtrend < bullbearline
    whitecandle := true
    White_highprice := high
    White_closeprice := close
    White_lowprice := low
    White_lowprice

var bool greencandle = false
var float Green_highprice = na
var float Green_closeprice = na
var float Green_lowprice = na
if fundtrend > bullbearline
    greencandle := true
    Green_highprice := high
    Green_closeprice := close
    Green_lowprice := low

var bool redcandle = false
var float Red_highprice = na
var float Red_closeprice = na
var float Red_lowprice = na
if fundtrend < bullbearline
    redcandle := true
    Red_highprice := high
    Red_closeprice := close
    Red_lowprice := low

var bool bluecandle = false
var float Blue_highprice = na
var float Blue_closeprice = na
var float Blue_lowprice = na
if fundtrend < bullbearline and fundtrend > xrf(fundtrend * 0.95, 1)
    bluecandle := true
    Blue_highprice := high
    Blue_closeprice := close
    Blue_lowprice := low

// ======== 魔鬼滚仓风控检查和强制平仓 ========
if strategy.position_size != 0
    // 魔鬼滚仓动态止损
    if should_stop_loss
        strategy.close_all(comment = '🔥动态止损')
        consecutive_losses := consecutive_losses + 1
        consecutive_wins := 0

    // 提现熔断
    if should_withdraw
        strategy.close_all(comment = '💰提现熔断')

    // 传统风控保留
    if (enable_daily_limit and daily_trades >= max_daily_trades)
        strategy.close_all(comment = '传统风控平仓')
        trading_paused := true

// ======== 魔鬼滚仓交易执行系统 ========
if in_test_period() and can_trade and (not tradingtime or in_trading_time())

    // 首单开仓 - Banker策略信号
    if strategy.position_size == 0
        if is_long_allowed and ((yellowcandle and close > Yellow_highprice) or (bluecandle and close > Blue_highprice))
            base_position_size = calculate_position_size(0)
            entry_price := close
            highestHigh := high
            entry_bar_time := timenow
            base_entry_price := close

            strategy.entry('🔥首单多', strategy.long, qty=base_position_size, comment='🔥首单开多')
            daily_trades := daily_trades + 1

        else if is_short_allowed and ((whitecandle and close <= White_lowprice) or (redcandle and close <= Red_lowprice)) and trade_direction != 'long'
            base_position_size = calculate_position_size(0)
            entry_price := close
            lowestLow := low
            entry_bar_time := timenow
            base_entry_price := close

            strategy.entry('🔥首单空', strategy.short, qty=base_position_size, comment='🔥首单开空')
            daily_trades := daily_trades + 1

    // 多头加仓系统
    if current_position_size > 0
        // 20%盈利档位加仓
        if can_add_long_1
            add_size_1 = calculate_position_size(1)
            strategy.entry('⚡加仓多1', strategy.long, qty=add_size_1, comment='⚡20%加仓多')

        // 50%盈利档位加仓
        if can_add_long_2
            add_size_2 = calculate_position_size(2)
            strategy.entry('🚀加仓多2', strategy.long, qty=add_size_2, comment='🚀50%加仓多')

        // 100%盈利雪崩模式
        if can_add_long_3
            add_size_3 = calculate_position_size(3)
            strategy.entry('💥雪崩多', strategy.long, qty=add_size_3, comment='💥雪崩模式多')

    // 空头加仓系统
    if current_position_size < 0
        // 20%盈利档位加仓
        if can_add_short_1
            add_size_1 = calculate_position_size(1)
            strategy.entry('⚡加仓空1', strategy.short, qty=add_size_1, comment='⚡20%加仓空')

        // 50%盈利档位加仓
        if can_add_short_2
            add_size_2 = calculate_position_size(2)
            strategy.entry('🚀加仓空2', strategy.short, qty=add_size_2, comment='🚀50%加仓空')

        // 100%盈利雪崩模式
        if can_add_short_3
            add_size_3 = calculate_position_size(3)
            strategy.entry('💥雪崩空', strategy.short, qty=add_size_3, comment='💥雪崩模式空')

// ======== 魔鬼滚仓可视化系统 ========
// 动态止损线显示
plot(avg_entry_price, color = color.new(color.white, 80), style = plot.style_linebr, linewidth = 1, title = '平均开仓价')
plot(dynamic_stop_price, color = color.new(color.red, 60), style = plot.style_linebr, linewidth = 2, title = '🔥动态止损价')

// 盈利档位标记
plotshape(can_add_long_1 or can_add_short_1, style=shape.triangleup, location=location.belowbar, color=color.yellow, size=size.small, title="⚡20%盈利加仓")
plotshape(can_add_long_2 or can_add_short_2, style=shape.triangleup, location=location.belowbar, color=color.orange, size=size.normal, title="🚀50%盈利加仓")
plotshape(can_add_long_3 or can_add_short_3, style=shape.triangleup, location=location.belowbar, color=color.red, size=size.large, title="💥雪崩模式")

// 风控状态标记
plotshape(freeze_trading, style=shape.xcross, location=location.abovebar, color=color.purple, size=size.large, title="🔒交易冻结")
plotshape(should_withdraw, style=shape.diamond, location=location.abovebar, color=color.blue, size=size.normal, title="💰提现信号")

// Banker策略信号标记（保留原有）
plotshape(yellowcandle, style=shape.circle, location=location.belowbar, color=color.yellow, size=size.tiny, title="黄色信号")
plotshape(bluecandle, style=shape.circle, location=location.belowbar, color=color.blue, size=size.tiny, title="蓝色信号")
plotshape(whitecandle, style=shape.circle, location=location.abovebar, color=color.white, size=size.tiny, title="白色信号")
plotshape(redcandle, style=shape.circle, location=location.abovebar, color=color.red, size=size.tiny, title="红色信号")

// 魔鬼滚仓信息面板
var table devil_info_table = table.new(position.top_right, 3, 10, bgcolor=color.new(color.black, 80), border_width=1)
if barstate.islast
    table.cell(devil_info_table, 0, 0, "🔥 魔鬼滚仓状态", text_color=color.red, text_size=size.normal)
    table.cell(devil_info_table, 1, 0, "", text_color=color.white)
    table.cell(devil_info_table, 2, 0, "", text_color=color.white)

    table.cell(devil_info_table, 0, 1, "当前盈利", text_color=color.white)
    table.cell(devil_info_table, 1, 1, str.tostring(current_profit_percent, "#.##") + "%", text_color=current_profit_percent > 0 ? color.green : color.red)
    table.cell(devil_info_table, 2, 1, str.tostring(current_profit_amount, "#.##") + "U", text_color=current_profit_amount > 0 ? color.green : color.red)

    table.cell(devil_info_table, 0, 2, "连胜次数", text_color=color.white)
    table.cell(devil_info_table, 1, 2, str.tostring(consecutive_wins), text_color=consecutive_wins >= consecutive_wins_trigger ? color.green : color.gray)
    table.cell(devil_info_table, 2, 2, "连败:" + str.tostring(consecutive_losses), text_color=consecutive_losses >= 2 ? color.red : color.gray)

    table.cell(devil_info_table, 0, 3, "当日盈利", text_color=color.white)
    table.cell(devil_info_table, 1, 3, str.tostring(daily_profit_percent, "#.##") + "%", text_color=daily_profit_percent > 0 ? color.green : color.red)
    table.cell(devil_info_table, 2, 3, should_withdraw ? "🚨提现" : "正常", text_color=should_withdraw ? color.red : color.green)

    table.cell(devil_info_table, 0, 4, "波动率", text_color=color.white)
    table.cell(devil_info_table, 1, 4, str.tostring(price_volatility, "#.##") + "%", text_color=price_volatility > volatility_threshold ? color.orange : color.gray)
    table.cell(devil_info_table, 2, 4, is_extreme_market ? "极端" : "正常", text_color=is_extreme_market ? color.red : color.green)

    table.cell(devil_info_table, 0, 5, "持仓层数", text_color=color.white)
    table.cell(devil_info_table, 1, 5, str.tostring(strategy.opentrades), text_color=strategy.opentrades > 3 ? color.orange : color.green)
    table.cell(devil_info_table, 2, 5, str.tostring(max_pyramid_levels), text_color=color.gray)

    table.cell(devil_info_table, 0, 6, "交易状态", text_color=color.white)
    table.cell(devil_info_table, 1, 6, freeze_trading ? "🔒冻结" : "🟢正常", text_color=freeze_trading ? color.red : color.green)
    table.cell(devil_info_table, 2, 6, is_golden_time ? "黄金时段" : "非交易时段", text_color=is_golden_time ? color.green : color.gray)

    table.cell(devil_info_table, 0, 7, "方向", text_color=color.white)
    table.cell(devil_info_table, 1, 7, current_position_size > 0 ? "多头" : current_position_size < 0 ? "空头" : "空仓", text_color=current_position_size > 0 ? color.green : current_position_size < 0 ? color.red : color.gray)
    table.cell(devil_info_table, 2, 7, trade_direction, text_color=color.yellow)

// 风险警告系统
if barstate.islast
    // 连败警告
    if consecutive_losses >= 2
        runtime.error("⚠️ 连败" + str.tostring(consecutive_losses) + "次，注意风险控制！")

    // 当日盈利过高警告
    if daily_profit_percent > 25
        runtime.error("🚨 当日盈利" + str.tostring(daily_profit_percent, "#.##") + "%，建议提现！")

    // 极端市场警告
    if is_extreme_market and strategy.position_size != 0
        runtime.error("⚡ 检测到极端市场，动态止损已激活！")

// 时间退出（保留）
holding_hours = (timenow - entry_bar_time) / (1000 * 60 * 60)
time_exit_condition = enable_time_exit and holding_hours >= max_holding_hours
if strategy.position_size > 0 and time_exit_condition
    strategy.close_all(comment = '⏰时间平多')
if strategy.position_size < 0 and time_exit_condition
    strategy.close_all(comment = '⏰时间平空')