//@version=5
strategy("OTT-Stoch Pro 完整版", overlay=true, pyramiding=0, 
         default_qty_type=strategy.percent_of_equity, 
         default_qty_value=100, initial_capital=1000,
         commission_value=0.05, commission_type=strategy.commission.percent)

// 回测日期设置
startDate = input.time(timestamp("2020-01-01"), "回测开始日期", confirm=true)
endDate = input.time(timestamp("2023-12-31"), "回测结束日期", confirm=true)
inDateRange = time >= startDate and time <= endDate

//≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡
// 原始OTT计算逻辑（保持完整）
//≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡
getMA(src, length, percent) =>
    ma = ta.sma(src, length)
    fark = ma * percent * 0.01
    longStop = ma - fark
    longStop := ma > longStop[1] ? math.max(longStop, longStop[1]) : longStop
    shortStop = ma + fark
    shortStop := ma < shortStop[1] ? math.min(shortStop, shortStop[1]) : shortStop
    dir = 1
    dir := nz(dir[1], dir)
    dir := dir == -1 and ma > shortStop[1] ? 1 : dir == 1 and ma < longStop[1] ? -1 : dir
    MT = dir == 1 ? longStop : shortStop
    ma > MT ? MT * (200 + percent) / 200 : MT * (200 - percent) / 200

OTT_Fast = getMA(close, 1, input.float(3.0, 'OTT快线偏移%'))
OTT_Slow = getMA(close, 1, input.float(10.0, 'OTT慢线偏移%'))

// 风险和止盈止损参数设置
max_risk = input.float(2.0, "单笔风险%", minval=0.1, maxval=10.0, step=0.1)
daily_risk = input.float(1.0, "日内风险%", minval=0.1, maxval=5.0, step=0.1)
stop_loss_pct = input.float(0.8, "止损百分比%", minval=0.1, maxval=5.0, step=0.1)
trail_points_pct = input.float(20.0, "追踪点数百分比%", minval=1.0, maxval=50.0, step=1.0)
trail_offset_pct = input.float(5.0, "追踪偏移百分比%", minval=1.0, maxval=20.0, step=1.0)

//≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡
// 增强风险控制模块
//≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡
var int lastTradeBar = 0
var bool riskTriggered = false
var int lossCounter = 0
var float dailyEquity = strategy.initial_capital

checkRisk() =>
    totalRiskBreach = (strategy.initial_capital - strategy.equity) >= strategy.initial_capital * (max_risk/100)
    dailyRiskBreach = (dailyEquity - strategy.equity) >= dailyEquity * (daily_risk/100)
    [totalRiskBreach, dailyRiskBreach]

executeTrade(direction) =>
    entryPrice = close
    qty = strategy.equity * (max_risk/100) / entryPrice  // 风险暴露
    
    if direction == "long"
        strategy.entry("L", strategy.long, qty=qty)
        strategy.exit("LX", "L", 
                     stop = entryPrice * (1 - stop_loss_pct/100),
                     trail_points = entryPrice * (trail_points_pct/100),
                     trail_offset = entryPrice * (trail_offset_pct/100))
    else
        strategy.entry("S", strategy.short, qty=qty)
        strategy.exit("SX", "S", 
                     stop = entryPrice * (1 + stop_loss_pct/100),
                     trail_points = entryPrice * (trail_points_pct/100),
                     trail_offset = entryPrice * (trail_offset_pct/100))
    true

//≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡
// 主交易逻辑
//≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡
if not riskTriggered and inDateRange  // 添加日期范围检查
    [totalRisk, dailyRisk] = checkRisk()
    
    if OTT_Fast > OTT_Slow and bar_index > lastTradeBar
        if executeTrade("long")
            lastTradeBar := bar_index
    else if OTT_Fast < OTT_Slow and bar_index > lastTradeBar
        if executeTrade("short")
            lastTradeBar := bar_index

    if totalRisk or dailyRisk
        strategy.close_all()
        riskTriggered := true

//≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡
// 可视化模块
//≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡
plot(OTT_Fast, "OTT Fast", color.new(#00FF00, 0), 2)
plot(OTT_Slow, "OTT Slow", color.new(#FF0000, 0), 2)
hline(0, "Zero Line", color.gray)