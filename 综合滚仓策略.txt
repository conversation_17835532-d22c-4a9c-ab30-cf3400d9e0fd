//@version=6
strategy(title="综合滚仓策略", overlay=true, pyramiding=100, default_qty_type=strategy.percent_of_equity, currency=currency.USD, initial_capital=10000)

//=== 用户输入参数 ===
// 基础参数
strategyType = input.string("雨后的夏天", "策略类型", options=["雨后的夏天", "午饭浮云", "李法师Tony", "肥宅", "午饭维持", "Fil浮盈"])
tradeDirection = input.string("多空双向", "交易方向", options=["仅多", "仅空", "多空双向"])
confirmBars = input.int(1, "信号确认K线数", minval=1, maxval=4)
exchangeLeverage = input.float(100, "交易所杠杆", minval=1)
capitalPercent = input.float(2, "资金百分比(%)", minval=0.1, maxval=100) / 100
stopLossPerc = input.float(2, "止损比例(%)", minval=0.1, maxval=100) / 100

//=== 全局变量声明 ===
var float dynamicLeverage = 50.0
var int leverageStep = 0
var float compoundEquity = strategy.initial_capital
var int entryCount = 0
var float stopPrice = na
var bool positionActive = false

//=== 通用计算模块 ===
// 均线系统
ma1 = ta.sma(close, 30)
ma2 = ta.sma(close, 45)
ma3 = ta.sma(close, 60)
plot(ma1, color=color.blue, title="30周期均线")
plot(ma2, color=color.orange, title="45周期均线")
plot(ma3, color=color.purple, title="60周期均线")

// 信号生成
isAboveMA = close > ma1 and close > ma2 and close > ma3
isBelowMA = close < ma1 and close < ma2 and close < ma3

longSignal = ta.barssince(isAboveMA) < confirmBars and (tradeDirection == "仅多" or tradeDirection == "多空双向")
shortSignal = ta.barssince(isBelowMA) < confirmBars and (tradeDirection == "仅空" or tradeDirection == "多空双向")

//=== 杠杆计算函数 ===
getLeverage() =>
    [newLeverage, newStep] = switch strategyType
        "雨后的夏天" =>
            lev = dynamicLeverage * 0.8
            lev := math.max(lev, 1.2)
            [lev, na]
        "午饭浮云" =>
            steps = array.from(50, 25, 15, 10, 8, 6, 5, 4, 3, 2, 1.2)
            step = math.min(leverageStep + 1, array.size(steps) - 1)
            [array.get(steps, step), step]
        => 
            [dynamicLeverage, na]

//=== 仓位计算函数 ===
getPositionSize() =>
    size = switch strategyType
        "Fil浮盈"    => compoundEquity * capitalPercent * exchangeLeverage
        "李法师Tony" => strategy.equity * 0.1
        "肥宅"       => strategy.equity * 0.02
        "午饭维持"   => (2000000 - strategy.position_size) / close
        => strategy.equity * capitalPercent * dynamicLeverage

    math.round(size / syminfo.mintick) * syminfo.mintick

//=== 订单执行逻辑 ===
// 主入场信号
if (longSignal or shortSignal) and entryCount < (strategyType == "肥宅" ? 100 : strategyType == "雨后的夏天" ? 10 : 1)
    if longSignal
        strategy.entry("MainLong", strategy.long, qty=getPositionSize())
    if shortSignal
        strategy.entry("MainShort", strategy.short, qty=getPositionSize())
    
    // 更新杠杆状态
    [newLev, newStep] = getLeverage()
    dynamicLeverage := newLev
    if not na(newStep)
        leverageStep := newStep
    
    entryCount += 1
    positionActive := true

// 肥宅高频加仓
if strategyType == "肥宅" and ta.change(close) > 0.01
    strategy.order("AddLong", strategy.long, qty=getPositionSize())

// 复利模式更新
if strategyType == "Fil浮盈" and strategy.position_size > 0
    compoundEquity := strategy.equity

// 强制维持仓位
if strategyType == "午饭维持"
    targetQty = 2000000
    if strategy.position_size < targetQty
        strategy.entry("Fill", strategy.long, qty=targetQty - strategy.position_size)

// 李法师Tony止损
if strategyType == "李法师Tony" and positionActive
    stopPrice := strategy.position_avg_price * (1 - stopLossPerc)
    strategy.exit("StopLoss", "MainLong", stop=stopPrice)

//=== 可视化 ===
plot(strategy.equity, title="资产曲线", color=color.green)
plot(dynamicLeverage, title="动态杠杆", color=color.red)