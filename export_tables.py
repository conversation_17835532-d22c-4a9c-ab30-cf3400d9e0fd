import re
import pandas as pd

def extract_markdown_tables(text):
    # 匹配Markdown表格
    table_pattern = re.compile(
        r'((?:\|.*\|\n)+)'  # 表格内容
        r'(?:\n|$)',        # 以换行或结尾
        re.MULTILINE
    )
    tables = table_pattern.findall(text)
    return tables

def markdown_table_to_df(table_str):
    lines = [line.strip() for line in table_str.strip().split('\n') if line.strip()]
    if len(lines) < 2:
        return None
    header = [col.strip() for col in lines[0].split('|')[1:-1]]
    data = []
    for line in lines[2:]:  # 跳过分隔线
        row = [col.strip() for col in line.split('|')[1:-1]]
        if len(row) == len(header):
            data.append(row)
    return pd.DataFrame(data, columns=header)

def export_tables_to_excel(md_path, excel_path):
    with open(md_path, 'r', encoding='utf-8') as f:
        content = f.read()
    tables = extract_markdown_tables(content)
    writer = pd.ExcelWriter(excel_path, engine='openpyxl')
    for idx, table in enumerate(tables):
        df = markdown_table_to_df(table)
        if df is not None:
            sheet_name = f'Table{idx+1}'
            df.to_excel(writer, sheet_name=sheet_name, index=False)
    writer.close()
    print(f"已导出{len(tables)}个表格到 {excel_path}")

if __name__ == '__main__':
    md_file = r'd:\pine\30天A股超短交易计划.txt'
    excel_file = r'd:\pine\30天A股超短交易计划.xlsx'
    export_tables_to_excel(md_file, excel_file)