//@version=6
strategy('Banker1.6', overlay = true, pyramiding = 1, initial_capital = 1000, default_qty_type = strategy.percent_of_equity, default_qty_value = 100, commission_type = strategy.commission.percent, commission_value = 0.07)
// ======== 输入参数 ========
trade_direction = input.string('both', '交易方向', options = ['long', 'short', 'both'])
is_long_allowed = trade_direction == 'long' or trade_direction == 'both'
is_short_allowed = trade_direction == 'short' or trade_direction == 'both'

// 止损参数
var group_stop = '止损参数'
selected_stop_type = input.string('无', title = '止损类型', options = ['固定止损', '移动止损', '无'], group = group_stop)
fixed_stop_loss_pct = input.float(0.8, '固定止损%', group = group_stop, minval = 0, maxval = 100) / 100
fixed_take_profit_pct = input.float(60.0, '固定止盈%', group = group_stop, minval = 0, maxval = 2000) / 100
trailing_start_pct = input.float(4.0, '移动止盈触发%', group = group_stop, minval = 0, maxval = 2000) / 100
trailing_stop_pct = input.float(1.0, '移动止损回调%', group = group_stop, minval = 0, maxval = 100) / 100
immediate_stop = input.bool(true, '使用开仓初始止损', group = group_stop)
initial_stop_loss_pct = input.float(0.6, '初始止损%', group = group_stop, minval = 0, maxval = 100) / 100
breakeven = input.bool(title="使用盈亏平衡", defval=true, group = group_stop)
bet_target = input.int(title="Break Even Target", defval=2, options=[1, 2, 3, 4], group = group_stop)

var group_pyramid_exit = '分批止盈设置'
enable_pyramid_exit = input.bool(true, '启用分批止盈', group = group_pyramid_exit) // 新增启用开关
exit1_profit = input.float(6.0, '第1阶段浮盈%', group = group_pyramid_exit, minval = 0, maxval = 500) / 100
exit1_qty = input.float(15.0, '第1阶段止盈比例%', group = group_pyramid_exit, minval = 0, maxval = 100) / 100
exit2_profit = input.float(12.0, '第2阶段浮盈%', group = group_pyramid_exit, minval = 0, maxval = 500) / 100
exit2_qty = input.float(25.0, '第2阶段止盈比例%', group = group_pyramid_exit, minval = 0, maxval = 100) / 100
exit3_profit = input.float(22.0, '第3阶段浮盈%', group = group_pyramid_exit, minval = 0, maxval = 500) / 100
exit3_qty = input.float(30.0, '第3阶段止盈比例%', group = group_pyramid_exit, minval = 0, maxval = 100) / 100

// 风险控制
var group_risk = '风险控制'
enable_daily_limit = input.bool(true, '启用每日交易限制', group = group_risk)
max_daily_trades = input.int(8, '最大每日交易次数', group = group_risk, minval = 1)
enable_drawdown = input.bool(true, '启用最大回撤控制', group = group_risk)
max_drawdown_pct = input.float(4, '最大回撤%', group = group_risk, minval = 0, maxval = 100) / 100
recovery_threshold_pct = input.float(2, '恢复阈值%', group = group_risk, minval = 0.1, maxval = 100) / 100
enable_daily_loss = input.bool(true, '启用每日最大亏损', group = group_risk)
daily_max_loss_pct = input.float(2, '每日最大亏损%', group = group_risk, minval = 0, maxval = 50) / 100
min_pause_hours = input.int(8, '最小暂停时间(小时)', group = group_risk, minval = 1)

// 时间退出
var group_time_exit = '时间退出'
enable_time_exit = input.bool(true, '启用最大持仓时间', group = group_time_exit)
max_holding_hours = input.int(24, '最大持仓时间(小时)', group = group_time_exit, minval = 1)

// 回测时间
var group_time = '回测时间'
testStartYear = input.int(2025, '开始年份', group = group_time)
testStartMonth = input.int(3, '开始月份', group = group_time)
testStartDay = input.int(1, '开始日', group = group_time)
testStopYear = input.int(2099, '结束年份', group = group_time)
testStopMonth = input.int(12, '结束月份', group = group_time)
testStopDay = input.int(31, '结束日', group = group_time)

// ======== 全局变量 ========
var float commission = 0.07
varip float entry_price = na
varip float highestHigh = na
varip float lowestLow = na
varip int daily_trades = 0
varip float daily_equity_start = strategy.initial_capital
varip string last_trade_day = na
varip bool trading_paused = false
varip int pause_start_time = 0
varip float base_equity_after_stop = na
varip float max_normal_equity = strategy.initial_capital
varip bool recovery_mode = false
varip float current_equity = na
varip int current_time = na
varip string current_exchange_day = na
varip int entry_bar_time = na
varip bool daily_loss_breached = false
varip bool exit1_triggered = false
varip bool exit2_triggered = false
varip bool exit3_triggered = false

// 交易时段
var group_trading_time = '交易时段'
tradingtime = input.bool(true, '启用交易时段', group = group_trading_time)
asia_session = input.bool(true, '亚洲时段 09:00-13:00', group = group_trading_time)
london_session = input.bool(true, '伦敦时段 15:00-18:00', group = group_trading_time)
us_am_session = input.bool(true, '美国早盘 21:30-00:00', group = group_trading_time)
us_pm_session = input.bool(true, '美国下午 02:30-05:00', group = group_trading_time)
custom_session = input.bool(false, '启用自定义时段', group = group_trading_time)

// 自定义时段参数（仅在启用时显示）
custom_start_h = input.int(7, '开始小时', group = group_trading_time, minval=0, maxval=23, tooltip='仅在启用自定义时段时有效')
custom_start_m = input.int(0, '开始分钟', group = group_trading_time, minval=0, maxval=59)
custom_end_h = input.int(9, '结束小时', group = group_trading_time, minval=0, maxval=24)
custom_end_m = input.int(0, '结束分钟', group = group_trading_time, minval=0, maxval=59)
timezone_offset = input.int(8, '时区偏移', group = group_trading_time)

// ======== 时间判断函数 ========
in_session(s_hour, s_minute, e_hour, e_minute) =>
    exchange_time = time + timezone_offset * 60 * 60 * 1000
    exchange_h = hour(exchange_time)
    exchange_m = minute(exchange_time)
    
    // 处理跨日情况
    start_day_offset = s_hour < e_hour ? 0 : 1
    end_day_offset = s_hour < e_hour ? 0 : 1
    (exchange_h > s_hour or (exchange_h == s_hour and exchange_m >= s_minute)) and (exchange_h < e_hour or (exchange_h == e_hour and exchange_m <= e_minute))

// ======== 核心逻辑 ========
in_test_period() =>
    time >= timestamp(testStartYear, testStartMonth, testStartDay, 0, 0) and time <= timestamp(testStopYear, testStopMonth, testStopDay, 23, 59)

// 夏令时判断函数
is_dst() =>
    year_var = year(time)
    // 3月的第二个星期日（夏令时开始）
    march_date = timestamp(year_var, 3, 8) // 从3月8日开始找
    march_day = dayofweek(march_date) == dayofweek.sunday ? march_date : march_date + (7 - dayofweek(march_date) + 1) * 86400000
    dst_start = march_day + (7 * 86400000) // 加7天得到第二个星期日
    
    // 11月的第一个星期日（夏令时结束）
    november_date = timestamp(year_var, 11, 1)
    november_day = dayofweek(november_date) == dayofweek.sunday ? november_date : november_date + (7 - dayofweek(november_date) + 1) * 86400000
    dst_end = november_day
    
    time >= dst_start and time < dst_end

// 修改美国时段判断逻辑
get_us_session() =>
    var int us_am_start_h = is_dst() ? 21 : 22  // 北京时间
    var int us_am_start_m = is_dst() ? 30 : 30
    var int us_am_end_h = is_dst() ? 4 : 5      // 次日凌晨
    var int us_pm_start_h = is_dst() ? 2 : 3    // 美国下午时段对应北京时间
    var int us_pm_start_m = 30
    var int us_pm_end_h = is_dst() ? 5 : 6
    
    // 美国早盘时段（包含跨日判断）
    us_am = us_am_session and (in_session(us_am_start_h, us_am_start_m, 24, 0) or in_session(0, 0, us_am_end_h, 0))
    
    // 美国下午时段（次日凌晨）
    us_pm = us_pm_session and in_session(us_pm_start_h, us_pm_start_m, us_pm_end_h, 0)
    
    us_am or us_pm

// 修改后的交易时段判断
in_trading_time() =>
    if not tradingtime
        true
    else
        asia = asia_session and in_session(9, 0, 13, 0)
        london = london_session and in_session(15, 0, 18, 0)
        us = get_us_session()  // 调用新的美国时段判断
        custom = custom_session and in_session(custom_start_h, custom_start_m, custom_end_h, custom_end_m)
        
        asia or london or us or custom

can_trade = not trading_paused and (not enable_daily_limit or daily_trades < max_daily_trades)

// 每日重置逻辑
current_exchange_day := str.tostring(year) + '-' + str.tostring(month) + '-' + str.tostring(dayofmonth)
if current_exchange_day != last_trade_day
    daily_trades := 0
    daily_equity_start := strategy.equity
    last_trade_day := current_exchange_day
    trading_paused := false
    daily_loss_breached := false
    max_normal_equity := strategy.initial_capital
    max_normal_equity

// 每日亏损检测
daily_loss = enable_daily_loss ? (daily_equity_start - strategy.equity) / daily_equity_start : 0.0
daily_loss_breached := enable_daily_loss and daily_loss >= daily_max_loss_pct and daily_max_loss_pct > 0
if daily_loss_breached
    trading_paused := true
    trading_paused

// 回撤控制
if enable_drawdown
    if not recovery_mode
        max_normal_equity := math.max(max_normal_equity, strategy.equity)
        drawdown = (max_normal_equity - strategy.equity) / max_normal_equity
        if drawdown >= max_drawdown_pct and strategy.position_size != 0
            strategy.close_all()
            base_equity_after_stop := strategy.equity
            recovery_mode := true
            trading_paused := true
            pause_start_time := timenow
            pause_start_time
    if recovery_mode
        recovery_return = (strategy.equity - base_equity_after_stop) / base_equity_after_stop
        pause_duration = (timenow - pause_start_time) / (1000 * 60 * 60)
        if recovery_return >= recovery_threshold_pct or pause_duration >= min_pause_hours
            recovery_mode := false
            trading_paused := false
            max_normal_equity := strategy.equity
            max_normal_equity

// 主体逻辑：Banker策略信号
//functions
xrf(values, length) =>
    r_val = float(na)
    if length >= 1
        for i = 0 to length by 1
            if na(r_val) or not na(values[i])
                r_val := values[i]
                r_val
    r_val

xsa(src, len, wei) =>
    sumf = 0.0
    ma = 0.0
    out = 0.0
    sumf := nz(sumf[1]) - nz(src[len]) + src
    ma := na(src[len]) ? na : sumf / len
    out := na(out[1]) ? ma : (src * wei + out[1] * (len - wei)) / len
    out

//set up a simple model of banker fund flow trend	
fundtrend = (3 * xsa((close - ta.lowest(low, 27)) / (ta.highest(high, 27) - ta.lowest(low, 27)) * 100, 5, 1) - 2 * xsa(xsa((close - ta.lowest(low, 27)) / (ta.highest(high, 27) - ta.lowest(low, 27)) * 100, 5, 1), 3, 1) - 50) * 1.032 + 50
//define typical price for banker fund
typ = (2 * close + high + low + open) / 5
//lowest low with mid term fib # 34
lol = ta.lowest(low, 34)
//highest high with mid term fib # 34
hoh = ta.highest(high, 34)
//define banker fund flow bull bear line
bullbearline = ta.ema((typ - lol) / (hoh - lol) * 100, 13)
//define banker entry signal
bankerentry = ta.crossover(fundtrend, bullbearline) and bullbearline < 25

// 定义蜡烛颜色和价格
var bool yellowcandle = false
var float Yellow_highprice = na
var float Yellow_closeprice = na
var float Yellow_lowprice = na
if bankerentry
    yellowcandle := true
    Yellow_highprice := high
    Yellow_closeprice := close
    Yellow_lowprice := low
    Yellow_lowprice

var bool whitecandle = false
var float White_highprice = na
var float White_closeprice = na
var float White_lowprice = na
if fundtrend < bullbearline
    whitecandle := true
    White_highprice := high
    White_closeprice := close
    White_lowprice := low
    White_lowprice

var bool greencandle = false
var float Green_highprice = na
var float Green_closeprice = na
var float Green_lowprice = na
if fundtrend > bullbearline
    greencandle := true
    Green_highprice := high
    Green_closeprice := close
    Green_lowprice := low

var bool redcandle = false
var float Red_highprice = na
var float Red_closeprice = na
var float Red_lowprice = na
if fundtrend < bullbearline
    redcandle := true
    Red_highprice := high
    Red_closeprice := close
    Red_lowprice := low    

var bool bluecandle = false
var float Blue_highprice = na
var float Blue_closeprice = na
var float Blue_lowprice = na
if fundtrend < bullbearline and fundtrend > xrf(fundtrend * 0.95, 1)
    bluecandle := true
    Blue_highprice := high
    Blue_closeprice := close
    Blue_lowprice := low

// 风控检查和强制平仓（移到最前面）
if strategy.position_size != 0
    if (enable_daily_loss and daily_loss >= daily_max_loss_pct) or 
       (enable_drawdown and (max_normal_equity - strategy.equity) / max_normal_equity >= max_drawdown_pct) or
       (enable_daily_limit and daily_trades >= max_daily_trades)
        strategy.close_all(comment = '风控平仓')
        trading_paused := true
        // 重置分批止盈状态
        exit1_triggered := false
        exit2_triggered := false
        exit3_triggered := false
        if enable_drawdown
            base_equity_after_stop := strategy.equity
            recovery_mode := true
            pause_start_time := timenow

// 开仓条件
if in_test_period() and can_trade and (not tradingtime or in_trading_time())
    if strategy.position_size == 0
        // if is_long_allowed and yellowcandle and close > Yellow_highprice
        if is_long_allowed and ((yellowcandle and close > Yellow_highprice) or (bluecandle and close > Blue_highprice))
        // if is_long_allowed and bluecandle and close > Blue_highprice               
            entry_price := close
            highestHigh := high
            entry_bar_time := timenow
            // 重置分批止盈状态
            exit1_triggered := false
            exit2_triggered := false
            exit3_triggered := false
            
            strategy.entry('Long', strategy.long, comment = '开多')
            daily_trades := daily_trades + 1
            if selected_stop_type == '固定止损'
                float breakeven_target = entry_price * (1 + fixed_stop_loss_pct * bet_target)
                float breakeven_stop = entry_price * (1 + commission / 100)
                float stop_price = entry_price * (1 - fixed_stop_loss_pct)
                if breakeven and high >= breakeven_target
                    stop_price := breakeven_stop
                strategy.exit('Long Exit', 'Long', stop = stop_price, limit = not enable_pyramid_exit ? entry_price * (1 + fixed_take_profit_pct) : na, comment = stop_price == breakeven_stop ? '盈亏平衡' : '固定止损止盈')
            else if selected_stop_type == '移动止损'
                strategy.exit('Long Trail', 'Long', stop = entry_price * (1 - initial_stop_loss_pct), comment = '移损')

        // else if is_short_allowed and whitecandle and close <= White_lowprice and trade_direction != 'long'
        else if is_short_allowed and ((whitecandle and close <= White_lowprice) or (redcandle and close <= Red_lowprice)) and trade_direction != 'long'            
            entry_price := close
            lowestLow := low
            entry_bar_time := timenow
            // 重置分批止盈状态
            exit1_triggered := false
            exit2_triggered := false
            exit3_triggered := false
            
            strategy.entry('Short', strategy.short, comment = '开空')
            daily_trades := daily_trades + 1
            if selected_stop_type == '固定止损'
                float breakeven_target = entry_price * (1 - fixed_stop_loss_pct * bet_target)
                float breakeven_stop = entry_price * (1 - commission / 100)
                float stop_price = entry_price * (1 + fixed_stop_loss_pct)
                if breakeven and low <= breakeven_target
                    stop_price := breakeven_stop
                strategy.exit('Short Exit', 'Short', stop = stop_price,limit = not enable_pyramid_exit ? entry_price * (1 - fixed_take_profit_pct) : na,comment = stop_price == breakeven_stop ? '盈亏平衡' : '固定止损止盈')
            else if selected_stop_type == '移动止损'
                strategy.exit('Short Trail', 'Short', stop = entry_price * (1 + initial_stop_loss_pct), comment = '移损')

// 更新固定止损和移动止损
if strategy.position_size > 0 and not trading_paused
    highestHigh := math.max(high, highestHigh)
    if selected_stop_type == '固定止损'
        float breakeven_target = entry_price * (1 + fixed_stop_loss_pct * bet_target)
        float breakeven_stop = entry_price * (1 + commission / 100)
        float stop_price = entry_price * (1 - fixed_stop_loss_pct)
        if breakeven and high >= breakeven_target
            stop_price := breakeven_stop
        strategy.exit('Long Exit', 'Long', stop = stop_price,limit = not enable_pyramid_exit ? entry_price * (1 + fixed_take_profit_pct) : na, comment = stop_price == breakeven_stop ? '盈亏平衡' : '固定止损止盈')
    else if selected_stop_type == '移动止损'
        float breakeven_target = entry_price * (1 + initial_stop_loss_pct * bet_target)
        float breakeven_stop = entry_price * (1 + commission / 100)
        trail_activate = highestHigh >= entry_price * (1 + trailing_start_pct)
        float trail_stop = trail_activate ? highestHigh * (1 - trailing_stop_pct) : entry_price * (1 - initial_stop_loss_pct)
        float stop_price = trail_stop
        if breakeven and high >= breakeven_target
            stop_price := math.max(breakeven_stop, trail_stop)
        strategy.exit('Long Trail', 'Long', stop = stop_price, comment = stop_price == breakeven_stop ? '盈亏平衡' : '移损')
else if strategy.position_size < 0 and not trading_paused
    lowestLow := math.min(low, lowestLow)
    if selected_stop_type == '移动止损'
        float breakeven_target = entry_price * (1 - initial_stop_loss_pct * bet_target)
        float breakeven_stop = entry_price * (1 - commission / 100)
        trail_activate = lowestLow <= entry_price * (1 - trailing_start_pct)
        float trail_stop = trail_activate ? lowestLow * (1 + trailing_stop_pct) : entry_price * (1 + initial_stop_loss_pct)
        float stop_price = trail_stop
        if breakeven and low <= breakeven_target
            stop_price := math.min(breakeven_stop, trail_stop)
        strategy.exit('Short Trail', 'Short', stop = stop_price, comment = stop_price == breakeven_stop ? '盈亏平衡' : '移损')

// 修改多头分批止盈逻辑
if strategy.position_size > 0 and enable_pyramid_exit and not trading_paused
    float unrealized_pct = (close - entry_price) / entry_price
    float current_position = math.abs(strategy.position_size)
    
    if not exit1_triggered and unrealized_pct >= exit1_profit
        float exit1_size = math.min(exit1_qty * current_position, current_position)
        strategy.order('Long Exit1', strategy.short, qty = exit1_size, comment = '一阶止盈')
        exit1_triggered := true
    
    if not exit2_triggered and unrealized_pct >= exit2_profit
        float exit2_size = math.min(exit2_qty * current_position, current_position)
        strategy.order('Long Exit2', strategy.short, qty = exit2_size, comment = '二阶止盈')
        exit2_triggered := true
    
    if not exit3_triggered and unrealized_pct >= exit3_profit
        strategy.close_all(comment = '三阶全平')
        exit3_triggered := true

    if exit1_qty == 1.0 or exit2_qty == 1.0 or exit3_qty == 1.0
        strategy.close_all(comment = '全额止盈')

// 修改空头分批止盈逻辑
if strategy.position_size < 0 and enable_pyramid_exit and not trading_paused
    float unrealized_pct_short = (entry_price - close) / entry_price
    float current_position = math.abs(strategy.position_size)
    
    if not exit1_triggered and unrealized_pct_short >= exit1_profit
        float exit1_size = math.min(exit1_qty * current_position, current_position)
        strategy.order('Short Exit1', strategy.long, qty = exit1_size, comment = '一阶止盈')
        exit1_triggered := true
    
    if not exit2_triggered and unrealized_pct_short >= exit2_profit
        float exit2_size = math.min(exit2_qty * current_position, current_position)
        strategy.order('Short Exit2', strategy.long, qty = exit2_size, comment = '二阶止盈')
        exit2_triggered := true
    
    if not exit3_triggered and unrealized_pct_short >= exit3_profit
        strategy.close_all(comment = '三阶全平')
        exit3_triggered := true

    if exit1_qty == 1.0 or exit2_qty == 1.0 or exit3_qty == 1.0
        strategy.close_all(comment = '全额止盈')

// 在平仓后重置状态
if strategy.position_size == 0
    exit1_triggered := false
    exit2_triggered := false
    exit3_triggered := false

// 时间退出
holding_hours = (timenow - entry_bar_time) / (1000 * 60 * 60)
time_exit_condition = enable_time_exit and holding_hours >= max_holding_hours
if strategy.position_size > 0 and time_exit_condition
    strategy.close('Long', comment = '平多')
if strategy.position_size < 0 and time_exit_condition
    strategy.close('Short', comment = '平空')