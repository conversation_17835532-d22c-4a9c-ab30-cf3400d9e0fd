//@version=5
strategy("OTT-Stoch Pro 完整版", overlay=true, pyramiding=0, 
         default_qty_type=strategy.percent_of_equity, 
         default_qty_value=100, initial_capital=1000,
         commission_value=0.05, commission_type=strategy.commission.percent)

//≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡
// 原始OTT计算逻辑（保持完整）
//≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡
getMA(src, length, percent) =>
    ma = ta.sma(src, length)
    fark = ma * percent * 0.01
    longStop = ma - fark
    longStop := ma > longStop[1] ? math.max(longStop, longStop[1]) : longStop
    shortStop = ma + fark
    shortStop := ma < shortStop[1] ? math.min(shortStop, shortStop[1]) : shortStop
    dir = 1
    dir := nz(dir[1], dir)
    dir := dir == -1 and ma > shortStop[1] ? 1 : dir == 1 and ma < longStop[1] ? -1 : dir
    MT = dir == 1 ? longStop : shortStop
    ma > MT ? MT * (200 + percent) / 200 : MT * (200 - percent) / 200

OTT_Fast = getMA(close, 1, input.float(3.0, 'OTT快线偏移%'))
OTT_Slow = getMA(close, 1, input.float(10.0, 'OTT慢线偏移%'))
max_risk = input.float(2.0, "单笔风险%")
//≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡
// 增强风险控制模块
//≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡
var int lastTradeBar = 0
var bool riskTriggered = false
var int lossCounter = 0
var float dailyEquity = strategy.initial_capital

checkRisk() =>
    totalRiskBreach = (strategy.initial_capital - strategy.equity) >= strategy.initial_capital * (max_risk/100)
    dailyRiskBreach = (dailyEquity - strategy.equity) >= dailyEquity * 0.01
    [totalRiskBreach, dailyRiskBreach]

executeTrade(direction) =>
    entryPrice = close
    qty = strategy.equity * (max_risk/100) / entryPrice  // 2%风险暴露
    
    if direction == "long"
        strategy.entry("L", strategy.long, qty=qty)
        strategy.exit("LX", "L", stop = entryPrice * 0.992,trail_points = entryPrice * 0.2,trail_offset = entryPrice * 0.05)
    else
        strategy.entry("S", strategy.short, qty=qty)
        strategy.exit("SX", "S", stop = entryPrice * 1.008,trail_points = entryPrice * 0.2,trail_offset = entryPrice * 0.05)
    true

//≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡
// 主交易逻辑
//≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡
if not riskTriggered
    [totalRisk, dailyRisk] = checkRisk()
    
    if OTT_Fast > OTT_Slow and bar_index > lastTradeBar
        if executeTrade("long")
            lastTradeBar := bar_index
    else if OTT_Fast < OTT_Slow and bar_index > lastTradeBar
        if executeTrade("short")
            lastTradeBar := bar_index

    if totalRisk or dailyRisk
        strategy.close_all()
        riskTriggered := true

//≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡
// 可视化模块
//≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡≡
plot(OTT_Fast, "OTT Fast", color.new(#00FF00, 0), 2)
plot(OTT_Slow, "OTT Slow", color.new(#FF0000, 0), 2)
hline(0, "Zero Line", color.gray)