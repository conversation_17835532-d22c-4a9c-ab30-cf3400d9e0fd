//@version=6
strategy('3MA12魔鬼滚仓版', overlay = true, pyramiding = 1, initial_capital = 1000, default_qty_type = strategy.percent_of_equity, default_qty_value = 100, process_orders_on_close = true)

// ======== 输入参数 ========
// 交易方向
trade_direction = input.string('both', '交易方向', options = ['long', 'short', 'both'])
is_long_allowed = trade_direction == 'long' or trade_direction == 'both'
is_short_allowed = trade_direction == 'short' or trade_direction == 'both'

// 添加信号确认参数
var group_signal = '信号确认'
enable_signal_confirm = input.bool(false, '启用信号确认', group = group_signal)

// 魔鬼滚仓风控系统
var group_risk = '魔鬼滚仓风控'
enable_devil_rolling = input.bool(true, "启用魔鬼滚仓模式", group = group_risk)

// 基础风控参数
initial_position_pct = input.float(7.5, "首单仓位%", group = group_risk, minval=5, maxval=10) / 100
base_stop_loss_pct = input.float(3.0, "基础止损%", group = group_risk, minval=1, maxval=5) / 100

// 盈利加仓参数
enable_profit_scaling = input.bool(true, "启用盈利加仓", group = group_risk)
profit_20_multiplier = input.float(1.5, "20%盈利加仓倍数", group = group_risk, minval=1, maxval=3)
profit_50_multiplier = input.float(2.0, "50%盈利加仓倍数", group = group_risk, minval=1, maxval=5)
profit_100_all_in = input.bool(true, "100%盈利全押", group = group_risk)

// 动态移动止损参数
dynamic_trailing_normal = input.float(7.0, "常规市场移动止损%", group = group_risk, minval=5, maxval=10) / 100
dynamic_trailing_extreme = input.float(4.0, "极端市场移动止损%", group = group_risk, minval=3, maxval=8) / 100
profit_step_normal = input.float(10.0, "常规市场盈利步长%", group = group_risk, minval=5, maxval=15) / 100
profit_step_extreme = input.float(5.0, "极端市场盈利步长%", group = group_risk, minval=3, maxval=10) / 100
volatility_threshold = input.float(15.0, "极端市场波动率阈值%", group = group_risk, minval=10, maxval=25) / 100

// 物理隔离控制
enable_physical_isolation = input.bool(true, "启用物理隔离控制", group = group_risk)
forced_rest_minutes = input.int(30, "强制休息时间(分钟)", group = group_risk, minval=15, maxval=60)
daily_profit_withdrawal = input.float(30.0, "日盈利提现触发%", group = group_risk, minval=20, maxval=50) / 100
consecutive_loss_limit = input.int(3, "连续亏损冻结次数", group = group_risk, minval=2, maxval=5)
freeze_hours = input.int(24, "冻结时间(小时)", group = group_risk, minval=12, maxval=48)

// 传统风控保留
enable_hard_drawdown = input.bool(false, "启用硬回撤限制", group = group_risk)
input_max_drawdown = input.float(15, "硬性最大回撤(%)", group = group_risk, minval=10, maxval=30)

// ======== 策略风控 ========
max_drawdown_value = enable_hard_drawdown ? input_max_drawdown : na
strategy.risk.max_drawdown(max_drawdown_value, strategy.percent_of_equity)

// 魔鬼滚仓止损系统
var group_stop = '魔鬼滚仓止损'
selected_stop_type = input.string('魔鬼动态止损', title = '止损类型', options = ['魔鬼动态止损', '固定止损', '移动止损', '无'], group = group_stop)

// 魔鬼滚仓专用参数
commission = input.float(title="手续费%", defval=0.07, minval=0, maxval=1, group = group_stop)

// 传统止损参数(备用)
fixed_stop_loss_pct = input.float(3.0, '固定止损%', group = group_stop, minval = 0, maxval = 100, inline = 'fixed_stop') / 100
fixed_take_profit_pct = input.float(30.0, '固定止盈%', group = group_stop, minval = 0, maxval = 2000, inline = 'fixed_stop') / 100
trailing_start_pct = input.float(6.0, '移动止盈触发%', group = group_stop, minval = 0, maxval = 2000, inline = 'trailing_stop') / 100
trailing_stop_pct = input.float(1.0, '移动止损回调%', group = group_stop, minval = 0, maxval = 100, inline = 'trailing_stop') / 100
initial_stop_loss_pct = input.float(3.0, '初始止损%', group = group_stop, minval = 0, maxval = 100) / 100

// 均线参数
var group_ma = '均线参数'
ma1_period = input.int(30, title = 'MA1周期', group = group_ma, minval = 1)
ma2_period = input.int(45, title = 'MA2周期', group = group_ma, minval = 1)
ma3_period = input.int(60, title = 'MA3周期', group = group_ma, minval = 1)


// 趋势过滤
var group_filter = '趋势过滤'
enable_adx_filter = input.bool(false, '启用ADX趋势过滤', group = group_filter)
di_length = input.int(14, 'DI周期', group = group_filter, minval = 1)
adx_smoothing = input.int(14, 'ADX平滑周期', group = group_filter, minval = 1)
adx_threshold = input.int(25, 'ADX强度阈值', group = group_filter, minval = 15, maxval = 50)

// 仓位管理
var group_position = '仓位管理'
enable_atr_sizing = input.bool(false, '启用波动仓位管理', group = group_position)
atr_period = input.int(14, 'ATR周期', group = group_position, minval = 5, maxval = 50)
risk_percent = input.float(1.0, '风险百分比%', group = group_position, minval = 0.1, maxval = 100) / 100

// 时间退出
var group_time_exit = '时间退出'
enable_time_exit = input.bool(true, '启用最大持仓时间', group = group_time_exit)
max_holding_hours = input.int(24, '最大持仓时间(小时)', group = group_time_exit, minval = 1)

// 时间设置
var group_time = '回测时间'
testStartYear = input.int(2025, '开始年份', group = group_time)
testStartMonth = input.int(1, '开始月份', group = group_time)
testStartDay = input.int(1, '开始日', group = group_time)
testStopYear = input.int(2099, '结束年份', group = group_time)
testStopMonth = input.int(12, '结束月份', group = group_time)
testStopDay = input.int(31, '结束日', group = group_time)

// 魔鬼滚仓交易时段
var group_trading_time = '魔鬼滚仓时段'
enable_devil_time_windows = input.bool(true, '启用魔鬼时间窗口', group = group_trading_time)

// 黄金时段 (UTC+8)
enable_golden_hours = input.bool(true, '启用黄金时段', group = group_trading_time)
golden_start_hour = input.int(9, '黄金时段开始小时', group = group_trading_time, minval=0, maxval=23)
golden_start_minute = input.int(30, '黄金时段开始分钟', group = group_trading_time, minval=0, maxval=59)
golden_end_hour = input.int(11, '黄金时段结束小时', group = group_trading_time, minval=0, maxval=23)
golden_end_minute = input.int(30, '黄金时段结束分钟', group = group_trading_time, minval=0, maxval=59)

// 美股窗口 (UTC+8 时间)
enable_us_window = input.bool(true, '启用美股窗口', group = group_trading_time)
us_pre_hour = input.int(21, '美股前窗口小时', group = group_trading_time, minval=0, maxval=23)
us_post_hour = input.int(23, '美股后窗口小时', group = group_trading_time, minval=0, maxval=23)

// 死亡时间
avoid_friday_night = input.bool(true, '避开周五晚间', group = group_trading_time)
friday_avoid_hour = input.int(20, '周五避开小时', group = group_trading_time, minval=0, maxval=23)

// 传统时段设置(备用)
tradingtime = input.bool(false, '使用传统时段', group = group_trading_time)
start_hour = input.int(8, '开始小时', group = group_trading_time)
start_minute = input.int(0, '开始分钟', group = group_trading_time)
end_hour = input.int(23, '结束小时', group = group_trading_time)
end_minute = input.int(59, '结束分钟', group = group_trading_time)
timezone_offset = input.int(8, '时区偏移', group = group_trading_time)

// ======== 全局变量 ========
// 基础变量
varip float entry_price = na
varip float highestHigh = na
varip float lowestLow = na
varip int daily_trades = 0
varip float daily_equity_start = strategy.initial_capital
varip string last_trade_day = na
varip bool trading_paused = false
varip int pause_start_time = 0
varip float current_equity = na
varip int current_time = na
varip string current_exchange_day = na
varip int entry_bar_time = na
varip bool hard_drawdown_triggered = false

// 信号检测变量
varip bool base_long_detected = false
varip bool base_short_detected = false
varip float key_long_high = na
varip float key_short_low = na

// 魔鬼滚仓专用变量
varip float initial_capital_for_trade = na
varip float accumulated_profit = 0.0
varip int consecutive_losses = 0
varip bool freeze_trading = false
varip int freeze_start_time = 0
varip int last_trade_time = 0
varip bool withdrawal_triggered = false
varip float dynamic_stop_price = na
varip float last_profit_level = 0.0
varip int win_streak = 0
varip bool extreme_market_mode = false
varip float position_scaling_factor = 1.0
varip float total_scaled_position = 0.0

// ======== 核心逻辑 ========
in_test_period() =>
    time >= timestamp(testStartYear, testStartMonth, testStartDay, 0, 0) and time <= timestamp(testStopYear, testStopMonth, testStopDay, 23, 59)

// 魔鬼滚仓时间窗口检测
in_devil_time_window() =>
    exchange_time = time + timezone_offset * 60 * 60 * 1000
    exchange_hour = hour(exchange_time)
    exchange_minute = minute(exchange_time)
    exchange_day = dayofweek(exchange_time)

    // 检查是否在黄金时段
    in_golden = enable_golden_hours and (
        exchange_hour > golden_start_hour or
        (exchange_hour == golden_start_hour and exchange_minute >= golden_start_minute)
    ) and (
        exchange_hour < golden_end_hour or
        (exchange_hour == golden_end_hour and exchange_minute <= golden_end_minute)
    )

    // 检查是否在美股窗口
    in_us_window = enable_us_window and (exchange_hour >= us_pre_hour and exchange_hour <= us_post_hour)

    // 检查死亡时间 - 周五晚间
    is_death_time = avoid_friday_night and exchange_day == dayofweek.friday and exchange_hour >= friday_avoid_hour

    // 最终时间窗口判断
    devil_time_ok = enable_devil_time_windows ? (in_golden or in_us_window) and not is_death_time : true
    devil_time_ok

// 传统时间窗口(备用)
in_trading_time() =>
    exchange_time = time + timezone_offset * 60 * 60 * 1000
    exchange_hour = hour(exchange_time)
    exchange_minute = minute(exchange_time)
    time_ok = exchange_hour > start_hour or exchange_hour == start_hour and exchange_minute >= start_minute
    time_ok := time_ok and (exchange_hour < end_hour or exchange_hour == end_hour and exchange_minute <= end_minute)
    time_ok

current_equity := strategy.equity
current_time := time
current_exchange_day := str.format('{0}-{1}-{2}', year(time + timezone_offset * 60 * 60 * 1000), month(time + timezone_offset * 60 * 60 * 1000), dayofmonth(time + timezone_offset * 60 * 60 * 1000))

[dip, din, adx_value] = ta.dmi(di_length, adx_smoothing)
valid_trend_long = not enable_adx_filter or adx_value >= adx_threshold and dip > din
valid_trend_short = not enable_adx_filter or adx_value >= adx_threshold and din > dip

atr_value = ta.atr(atr_period)
position_size = enable_atr_sizing ? strategy.equity * risk_percent / (atr_value * syminfo.pointvalue) : strategy.equity / close

if strategy.position_size == 0
    entry_bar_time := na
    entry_bar_time
else if na(entry_bar_time)
    entry_bar_time := current_time
    entry_bar_time

holding_hours = (current_time - entry_bar_time) / (1000 * 60 * 60)
time_exit_condition = enable_time_exit and holding_hours >= max_holding_hours

// 魔鬼滚仓风控系统
// 每日重置逻辑
if current_exchange_day != last_trade_day
    daily_trades := 0
    daily_equity_start := strategy.equity
    last_trade_day := current_exchange_day
    trading_paused := false
    withdrawal_triggered := false
    // 重置连续亏损计数(每日重置)
    if not freeze_trading
        consecutive_losses := 0

// 检测市场波动率(极端市场模式)
atr_current = ta.atr(14)
atr_avg = ta.sma(atr_current, 20)
current_volatility = atr_current / close
extreme_market_mode := current_volatility > volatility_threshold

// 物理隔离控制
// 1. 强制休息检测
time_since_last_trade = (current_time - last_trade_time) / (1000 * 60)  // 分钟
rest_period_ok = not enable_physical_isolation or time_since_last_trade >= forced_rest_minutes or last_trade_time == 0

// 2. 日盈利提现触发
daily_profit_pct = daily_equity_start > 0 ? (current_equity - daily_equity_start) / daily_equity_start : 0.0
if enable_physical_isolation and daily_profit_pct >= daily_profit_withdrawal and not withdrawal_triggered
    withdrawal_triggered := true
    trading_paused := true

// 3. 连续亏损冻结机制
if enable_physical_isolation and consecutive_losses >= consecutive_loss_limit and not freeze_trading
    freeze_trading := true
    freeze_start_time := current_time
    trading_paused := true

// 检查冻结解除
if freeze_trading
    freeze_duration = (current_time - freeze_start_time) / (1000 * 60 * 60)  // 小时
    if freeze_duration >= freeze_hours
        freeze_trading := false
        trading_paused := false
        consecutive_losses := 0

// 传统硬回撤控制(备用)
if enable_hard_drawdown and strategy.equity <= strategy.initial_capital * (1 - input_max_drawdown / 100)
    hard_drawdown_triggered := true
    if strategy.position_size != 0
        strategy.close_all(comment = "硬回撤平仓")
    trading_paused := true

// 最终交易许可
devil_can_trade = enable_devil_rolling ? (not trading_paused and rest_period_ok and not freeze_trading and not withdrawal_triggered) : true
can_trade = devil_can_trade

// 交易信号
ma1 = ta.sma(close, ma1_period)
ma2 = ta.sma(close, ma2_period)
ma3 = ta.sma(close, ma3_period)

// 基础信号条件
// base_long_condition = barstate.isconfirmed and close > ma1 and close > ma2 and close > ma3 and ma1 > ma2 and ma2 > ma3 and valid_trend_long
// base_short_condition = barstate.isconfirmed and close < ma1 and close < ma2 and close < ma3 and ma1 < ma2 and ma2 < ma3 and valid_trend_short
base_long_condition = barstate.isconfirmed and close > ma1 and close > ma2 and close > ma3 and valid_trend_long
base_short_condition = barstate.isconfirmed and close < ma1 and close < ma2 and close < ma3 and valid_trend_short

// 信号确认逻辑 - 修改为保持关键K线直到突破/跌破均线
if base_long_condition and strategy.position_size == 0 and not base_long_detected
    base_long_detected := true
    key_long_high := high

if base_short_condition and strategy.position_size == 0 and not base_short_detected
    base_short_detected := true
    key_short_low := low

// 最终信号
long_entry = enable_signal_confirm ? (base_long_detected and close > key_long_high) : base_long_condition
short_entry = enable_signal_confirm ? (base_short_detected and close < key_short_low) : base_short_condition

// 重置信号状态 - 只有在价格跌破/突破任意均线时才重置
if base_long_detected and (long_entry or close < ma1 or close < ma2 or close < ma3)
    base_long_detected := false

if base_short_detected and (short_entry or close > ma1 or close > ma2 or close > ma3)
    base_short_detected := false

// 当开仓后也需要重置状态
if strategy.position_size != 0
    base_long_detected := false
    base_short_detected := false

close_long = barstate.isconfirmed and (close < ma1 or close < ma2 or close < ma3)
close_short = barstate.isconfirmed and (close > ma1 or close > ma2 or close > ma3)

// 魔鬼滚仓仓位计算
calculate_devil_position_size() =>
    base_capital = strategy.equity

    if enable_devil_rolling
        // 首单固定仓位
        if strategy.position_size == 0
            initial_capital_for_trade := base_capital
            accumulated_profit := 0.0
            position_scaling_factor := 1.0
            total_scaled_position := 0.0

        // 计算当前盈利率
        if not na(initial_capital_for_trade) and initial_capital_for_trade > 0
            current_profit_pct = (base_capital - initial_capital_for_trade) / initial_capital_for_trade

            // 盈利加仓逻辑
            if enable_profit_scaling and current_profit_pct > 0
                if current_profit_pct >= 1.0 and profit_100_all_in  // 100%盈利全押
                    position_scaling_factor := base_capital / close
                else if current_profit_pct >= 0.5  // 50%盈利
                    profit_amount = base_capital - initial_capital_for_trade
                    additional_size = profit_amount * profit_50_multiplier / close
                    position_scaling_factor := (initial_capital_for_trade * initial_position_pct / close) + additional_size
                else if current_profit_pct >= 0.2  // 20%盈利
                    profit_amount = base_capital - initial_capital_for_trade
                    additional_size = profit_amount * profit_20_multiplier / close
                    position_scaling_factor := (initial_capital_for_trade * initial_position_pct / close) + additional_size
                else
                    position_scaling_factor := initial_capital_for_trade * initial_position_pct / close
            else
                position_scaling_factor := initial_capital_for_trade * initial_position_pct / close
        else
            position_scaling_factor := base_capital * initial_position_pct / close
    else
        // 传统仓位计算
        position_scaling_factor := enable_atr_sizing ? base_capital * risk_percent / (atr_value * syminfo.pointvalue) : base_capital / close

    position_scaling_factor

// 执行交易
final_time_check = enable_devil_time_windows ? in_devil_time_window() : (not tradingtime or in_trading_time())

if in_test_period() and can_trade and final_time_check
    if strategy.position_size == 0
        devil_position_size = calculate_devil_position_size()

        if is_long_allowed and long_entry
            entry_price := close
            highestHigh := high
            last_profit_level := 0.0

            strategy.entry('Long', strategy.long, qty = devil_position_size, comment = '魔鬼开多')
            daily_trades := daily_trades + 1
            last_trade_time := current_time

            // 魔鬼动态止损
            if selected_stop_type == '魔鬼动态止损'
                dynamic_stop_price := entry_price * (1 - base_stop_loss_pct)
                strategy.exit('Devil Long', 'Long', stop = dynamic_stop_price, comment = '魔鬼止损')
            else if selected_stop_type == '固定止损'
                strategy.exit('Long Exit', 'Long', stop = entry_price * (1 - fixed_stop_loss_pct), limit = entry_price * (1 + fixed_take_profit_pct), comment = '固损')
            else if selected_stop_type == '移动止损'
                strategy.exit('Long Trail', 'Long', stop = entry_price * (1 - initial_stop_loss_pct), comment = '移损')

        else if is_short_allowed and short_entry
            entry_price := close
            lowestLow := low
            last_profit_level := 0.0

            strategy.entry('Short', strategy.short, qty = devil_position_size, comment = '魔鬼开空')
            daily_trades := daily_trades + 1
            last_trade_time := current_time

            // 魔鬼动态止损
            if selected_stop_type == '魔鬼动态止损'
                dynamic_stop_price := entry_price * (1 + base_stop_loss_pct)
                strategy.exit('Devil Short', 'Short', stop = dynamic_stop_price, comment = '魔鬼止损')
            else if selected_stop_type == '固定止损'
                strategy.exit('Short Exit', 'Short', stop = entry_price * (1 + fixed_stop_loss_pct), limit = entry_price * (1 - fixed_take_profit_pct), comment = '固损')
            else if selected_stop_type == '移动止损'
                strategy.exit('Short Trail', 'Short', stop = entry_price * (1 + initial_stop_loss_pct), comment = '移损')

    // 魔鬼滚仓动态止损更新
    if strategy.position_size > 0 and not trading_paused
        highestHigh := math.max(high, highestHigh)
        current_profit_pct = (close - entry_price) / entry_price

        if selected_stop_type == '魔鬼动态止损'
            // 动态移动止损逻辑
            profit_step = extreme_market_mode ? profit_step_extreme : profit_step_normal
            trailing_pct = extreme_market_mode ? dynamic_trailing_extreme : dynamic_trailing_normal

            // 计算应该移动止损的盈利水平
            target_profit_level = math.floor(current_profit_pct / profit_step) * profit_step

            if target_profit_level > last_profit_level and target_profit_level > 0
                // 更新止损线
                new_stop_price = entry_price * (1 + target_profit_level - trailing_pct)
                dynamic_stop_price := math.max(dynamic_stop_price, new_stop_price)
                last_profit_level := target_profit_level
                strategy.exit('Devil Long', 'Long', stop = dynamic_stop_price, comment = '魔鬼动态止损')

        else if selected_stop_type == '固定止损'
            float stop_price = entry_price * (1 - fixed_stop_loss_pct)
            strategy.exit('Long Exit', 'Long', stop = stop_price, limit = entry_price * (1 + fixed_take_profit_pct), comment = '固定止损止盈')
        else if selected_stop_type == '移动止损'
            trail_activate = highestHigh >= entry_price * (1 + trailing_start_pct)
            float trail_stop = trail_activate ? highestHigh * (1 - trailing_stop_pct) : entry_price * (1 - initial_stop_loss_pct)
            strategy.exit('Long Trail', 'Long', stop = trail_stop, comment = '移损')

    else if strategy.position_size < 0 and not trading_paused
        lowestLow := math.min(low, lowestLow)
        current_profit_pct_short = (entry_price - close) / entry_price

        if selected_stop_type == '魔鬼动态止损'
            // 空头动态移动止损逻辑
            profit_step = extreme_market_mode ? profit_step_extreme : profit_step_normal
            trailing_pct = extreme_market_mode ? dynamic_trailing_extreme : dynamic_trailing_normal

            // 计算应该移动止损的盈利水平
            target_profit_level = math.floor(current_profit_pct_short / profit_step) * profit_step

            if target_profit_level > last_profit_level and target_profit_level > 0
                // 更新止损线
                new_stop_price = entry_price * (1 - target_profit_level + trailing_pct)
                dynamic_stop_price := math.min(dynamic_stop_price, new_stop_price)
                last_profit_level := target_profit_level
                strategy.exit('Devil Short', 'Short', stop = dynamic_stop_price, comment = '魔鬼动态止损')

        else if selected_stop_type == '固定止损'
            float stop_price = entry_price * (1 + fixed_stop_loss_pct)
            strategy.exit('Short Exit', 'Short', stop = stop_price, limit = entry_price * (1 - fixed_take_profit_pct), comment = '固定止损止盈')
        else if selected_stop_type == '移动止损'
            trail_activate = lowestLow <= entry_price * (1 - trailing_start_pct)
            float trail_stop = trail_activate ? lowestLow * (1 + trailing_stop_pct) : entry_price * (1 + initial_stop_loss_pct)
            strategy.exit('Short Trail', 'Short', stop = trail_stop, comment = '移损')

// 魔鬼滚仓平仓逻辑和盈亏统计
if strategy.position_size > 0 and (close_long or time_exit_condition)
    // 计算本次交易盈亏
    if not na(entry_price) and enable_devil_rolling
        trade_pnl_pct = (close - entry_price) / entry_price
        if trade_pnl_pct > 0
            win_streak := win_streak + 1
            consecutive_losses := 0
        else
            win_streak := 0
            consecutive_losses := consecutive_losses + 1
    strategy.close('Long', comment = '平多')

if strategy.position_size < 0 and (close_short or time_exit_condition)
    // 计算本次交易盈亏
    if not na(entry_price) and enable_devil_rolling
        trade_pnl_pct = (entry_price - close) / entry_price
        if trade_pnl_pct > 0
            win_streak := win_streak + 1
            consecutive_losses := 0
        else
            win_streak := 0
            consecutive_losses := consecutive_losses + 1
    strategy.close('Short', comment = '平空')

// 当策略平仓时也要统计盈亏
if strategy.position_size[1] != 0 and strategy.position_size == 0 and enable_devil_rolling
    if not na(entry_price)
        was_long = strategy.position_size[1] > 0
        final_pnl_pct = was_long ? (strategy.closedtrades.exit_price(strategy.closedtrades - 1) - entry_price) / entry_price :
                                   (entry_price - strategy.closedtrades.exit_price(strategy.closedtrades - 1)) / entry_price

        if final_pnl_pct > 0
            win_streak := win_streak + 1
            consecutive_losses := 0
        else
            win_streak := 0
            consecutive_losses := consecutive_losses + 1

// 可视化
plot(ma1, color = color.blue, title = "MA1")
plot(ma2, color = color.orange, title = "MA2")
plot(ma3, color = color.purple, title = "MA3")

// 魔鬼滚仓状态可视化
bgcolor(freeze_trading ? color.new(color.red, 80) : na, title = "冻结状态")
bgcolor(withdrawal_triggered ? color.new(color.green, 80) : na, title = "提现触发")
bgcolor(extreme_market_mode ? color.new(color.yellow, 90) : na, title = "极端市场")

// 动态止损线可视化
plot(strategy.position_size != 0 and not na(dynamic_stop_price) ? dynamic_stop_price : na,
     color = color.red, style = plot.style_line, linewidth = 2, title = "魔鬼动态止损线")

// 关键信号点标记
plotshape(base_long_detected and not na(key_long_high), style = shape.triangleup,
          location = location.belowbar, color = color.blue, size = size.small, title = "多头关键点")
plotshape(base_short_detected and not na(key_short_low), style = shape.triangledown,
          location = location.abovebar, color = color.red, size = size.small, title = "空头关键点")

// 魔鬼滚仓信息表格
if enable_devil_rolling and barstate.islast
    var table info_table = table.new(position.top_right, 2, 8, bgcolor = color.white, border_width = 1)
    table.cell(info_table, 0, 0, "魔鬼滚仓状态", text_color = color.black, text_size = size.small)
    table.cell(info_table, 1, 0, "", text_color = color.black, text_size = size.small)

    table.cell(info_table, 0, 1, "连续亏损", text_color = color.black, text_size = size.small)
    table.cell(info_table, 1, 1, str.tostring(consecutive_losses), text_color = color.black, text_size = size.small)

    table.cell(info_table, 0, 2, "连胜次数", text_color = color.black, text_size = size.small)
    table.cell(info_table, 1, 2, str.tostring(win_streak), text_color = color.black, text_size = size.small)

    table.cell(info_table, 0, 3, "日盈利率", text_color = color.black, text_size = size.small)
    table.cell(info_table, 1, 3, str.tostring(math.round(daily_profit_pct * 100, 2)) + "%", text_color = color.black, text_size = size.small)

    table.cell(info_table, 0, 4, "极端市场", text_color = color.black, text_size = size.small)
    table.cell(info_table, 1, 4, extreme_market_mode ? "是" : "否", text_color = color.black, text_size = size.small)

    table.cell(info_table, 0, 5, "冻结状态", text_color = color.black, text_size = size.small)
    table.cell(info_table, 1, 5, freeze_trading ? "冻结" : "正常", text_color = freeze_trading ? color.red : color.green, text_size = size.small)

    table.cell(info_table, 0, 6, "提现触发", text_color = color.black, text_size = size.small)
    table.cell(info_table, 1, 6, withdrawal_triggered ? "已触发" : "未触发", text_color = withdrawal_triggered ? color.green : color.gray, text_size = size.small)

    table.cell(info_table, 0, 7, "时间窗口", text_color = color.black, text_size = size.small)
    table.cell(info_table, 1, 7, final_time_check ? "开放" : "关闭", text_color = final_time_check ? color.green : color.red, text_size = size.small)
