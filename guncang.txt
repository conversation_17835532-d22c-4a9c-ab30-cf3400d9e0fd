
//@version=6
strategy("肥宅滚仓策略-完整版", 
     overlay=true,
     initial_capital=5000,
     default_qty_type=strategy.percent_of_equity,
     default_qty_value=100,
     commission_type=strategy.commission.percent,
     commission_value=0.075,
     pyramiding=5,
     process_orders_on_close=true,
     calc_on_every_tick=false)

// —————————— 用户参数输入界面 ——————————
inputGroup = "★ 基础设置 ★"
i_startDate   = input.time(timestamp("2020-01-01"), "回测开始时间", group=inputGroup)
i_endDate     = input.time(timestamp("2099-12-31"), "回测结束时间", group=inputGroup)
i_leverage    = input.int(5, "杠杆倍数", minval=1, maxval=100, group=inputGroup)
i_basePercent = input.float(1, "初始仓位比例(%)", minval=0.1, maxval=100, step=0.1, group=inputGroup) / 100

// ===== 输入参数分组方案 =====
// 使用独立变量名定义每个参数组
var GRP_BASIC   = "★ 基础设置 ★"
var GRP_TREND   = "★ 趋势参数 ★"
var GRP_ADD     = "★ 加仓参数 ★"
var GRP_RISK    = "★ 风控参数 ★"

// —— 趋势参数 —— 
i_maPeriod     = input.int(30, "均线周期", minval=5, maxval=200, group=GRP_TREND)
i_atrPeriod    = input.int(14, "ATR周期", minval=5, maxval=50, group=GRP_TREND)
i_trendConfirm = input.bool(true, "需要趋势确认", group=GRP_TREND)

// —— 加仓参数 ——
i_breakoutGap  = input.float(1.5, "突破加仓间距(ATR)", minval=0.5, group=GRP_ADD)
i_pullbackGap  = input.float(2.0, "回调加仓间距(ATR)", minval=1.0, group=GRP_ADD)
i_maxAddTimes  = input.int(3, "最大加仓次数", minval=1, maxval=5, group=GRP_ADD)

// —— 风控参数 ——
i_stopLoss     = input.float(1.5, "初始止损(ATR)", minval=0.5, group=GRP_RISK)
i_takeProfit   = input.float(10, "目标止盈(%)", minval=1, group=GRP_RISK) / 100
i_trailType    = input.string("ATR", "追踪类型", options=["ATR", "固定比例"], group=GRP_RISK)
i_trailValue   = input.float(1.5, "追踪幅度", minval=0.5, group=GRP_RISK)
// —————————— 核心指标计算 ——————————
// 趋势方向判断
ma = ta.sma(close, i_maPeriod)
marketTrend = ta.rising(ma, 2) ? 1 : ta.falling(ma, 2) ? -1 : 0

// 波动率计算
atr = ta.atr(i_atrPeriod)
normalizedATR = atr / close * 100  // 标准化为百分比

// 信号检测
doubleBottom = ta.lowest(low, 5) == ta.lowest(low, 10) and close > high[1]  
triangleBreak = (ta.highest(high, 5) - ta.lowest(low, 5)) < (atr * 1.5) and close > ta.highest(high[1], 5)

// 添加做空信号
doubleTop = ta.highest(high, 5) == ta.highest(high, 10) and close < low[1]
triangleDrop = (ta.highest(high, 5) - ta.lowest(low, 5)) < (atr * 1.5) and close < ta.lowest(low[1], 5)

// —————————— 仓位管理系统 ——————————
var bool isFirstEntry = true
var float baseEntryPrice = na
var int addCount = 0
var float dynamicStop = na

// 计算实际杠杆仓位
calcPositionSize() =>
    strategy.equity * i_basePercent * i_leverage

// 移动止损逻辑
trailingStop() =>
    trailAmount = i_trailType == "ATR" ? (atr * i_trailValue) : (close * i_trailValue / 100)
    longStop = close - trailAmount
    shortStop = close + trailAmount
    strategy.position_size > 0 ? longStop : shortStop

// —————————— 交易信号生成 ——————————
enterLongCondition = (doubleBottom or triangleBreak) and (marketTrend >= 0 or not i_trendConfirm)
enterShortCondition = (doubleTop or triangleDrop) and (marketTrend <= 0 or not i_trendConfirm)

addBreakoutCondition = strategy.position_size > 0 ? close > (baseEntryPrice + (atr * i_breakoutGap)) : 
     strategy.position_size < 0 ? close < (baseEntryPrice - (atr * i_breakoutGap)) : false
addPullbackCondition = strategy.position_size > 0 ? 
     (ta.crossover(close, ma) and (close - baseEntryPrice) > (atr * i_pullbackGap)) :
     strategy.position_size < 0 ? 
     (ta.crossunder(close, ma) and (baseEntryPrice - close) > (atr * i_pullbackGap)) : false

// —————————— 策略执行逻辑 ——————————
if time >= i_startDate and time <= i_endDate
    // 初始入场
    if strategy.position_size == 0
        if enterLongCondition
            baseEntryPrice := close
            strategy.entry("BaseLong", strategy.long, qty=calcPositionSize())
            dynamicStop := close - (atr * i_stopLoss)
            addCount := 0
        if enterShortCondition
            baseEntryPrice := close
            strategy.entry("BaseShort", strategy.short, qty=calcPositionSize())
            dynamicStop := close + (atr * i_stopLoss)
            addCount := 0

    // 突破加仓
    if strategy.position_size != 0 and addBreakoutCondition and addCount < i_maxAddTimes
        addCount += 1
        if strategy.position_size > 0
            strategy.entry("AddBreakout"+str.tostring(addCount), strategy.long, qty=calcPositionSize())
        else
            strategy.entry("AddBreakoutShort"+str.tostring(addCount), strategy.short, qty=calcPositionSize())
        dynamicStop := baseEntryPrice

    // 回调加仓
    if strategy.position_size != 0 and addPullbackCondition and addCount < i_maxAddTimes
        addCount += 1
        if strategy.position_size > 0
            strategy.entry("AddPullback"+str.tostring(addCount), strategy.long, qty=calcPositionSize())
        else
            strategy.entry("AddPullbackShort"+str.tostring(addCount), strategy.short, qty=calcPositionSize())
        dynamicStop := strategy.position_size > 0 ? math.max(dynamicStop, baseEntryPrice) : math.min(dynamicStop, baseEntryPrice)

    // 打印关键条件的状态
    if enterLongCondition
        label.new(bar_index, low, text="入场信号触发", color=color.green, style=label.style_label_up)
        label.new(bar_index, low, text="信号:\nDB=" + str.tostring(doubleBottom) + "\nTB=" + str.tostring(triangleBreak) + "\nTrend=" + str.tostring(marketTrend), color=color.green)
    if addBreakoutCondition
        label.new(bar_index, high, text="突破加仓信号", color=color.blue, style=label.style_label_down)
    
    if addPullbackCondition
        label.new(bar_index, low, text="回调加仓信号", color=color.orange, style=label.style_label_up)        
    
        // 动态止损管理
    if strategy.position_size > 0
)
        // 确保止损价格合理
        currentTrailStop = math.min(trailingStop(), close - (atr * 0.5))  // 防止止损太近
        dynamicStop := math.max(dynamicStop, currentTrailStop)     

        // 执行止损
        strategy.exit("TrailExit", "BaseLong", stop=dynamicStop)
        
        // 加仓部分止盈规则
        for i = 1 to i_maxAddTimes
            breakoutID = "AddBreakout"+str.tostring(i)
            strategy.exit(breakoutID+"_Exit", breakoutID, 
                 profit=baseEntryPrice * i_takeProfit,
                 stop=dynamicStop,
                 trail_offset=atr * 1.5)
            
            pullbackID = "AddPullback"+str.tostring(i)
            strategy.exit(pullbackID+"_Exit", pullbackID, 
                 profit=baseEntryPrice * i_takeProfit,
                 stop=dynamicStop,
                 trail_offset=atr * 1.5)

// —————————— 可视化工具 ——————————
plot(ma, "趋势均线", color=color.new(#00bcd4, 50), linewidth=2)
plot(dynamicStop, "动态止损", color=color.red, style=plot.style_linebr)


// 信号标记
plotshape(series=enterLongCondition, title="做多入场", style=shape.triangleup, location=location.belowbar, color=color.green, size=size.small)
plotshape(series=enterShortCondition, title="做空入场", style=shape.triangledown, location=location.abovebar, color=color.red, size=size.small)
plotshape(series=addBreakoutCondition and strategy.position_size > 0, title="做多突破加仓", style=shape.circle, location=location.abovebar, color=color.blue, size=size.tiny)
plotshape(series=addBreakoutCondition and strategy.position_size < 0, title="做空突破加仓", style=shape.circle, location=location.belowbar, color=color.purple, size=size.tiny)
plotshape(series=addPullbackCondition and strategy.position_size > 0, title="做多回调加仓", style=shape.circle, location=location.belowbar, color=color.orange, size=size.tiny)
plotshape(series=addPullbackCondition and strategy.position_size < 0, title="做空回调加仓", style=shape.circle, location=location.abovebar, color=color.yellow, size=size.tiny)