//@version=6

//声明==========================================================================
strategy(title = 'EMA33v6', overlay = true, precision = 2, pyramiding = 1, initial_capital = 10000, default_qty_type = strategy.percent_of_equity, default_qty_value = 100, slippage = 0, commission_type = strategy.commission.percent, commission_value = 0.04, calc_on_order_fills = false)

//设置==========================================================================
ma1_input = input.int(33, 'MA1', step = 1)
ma2_input = input.int(33, 'MA2', step = 1)
oppercent_1 = input.float(1.0, '突破MA1偏离度[%]', step = 0.1)
slpercent_1 = input.float(2.0, '止损[%]', step = 0.1)

//定义==========================================================================
ma1 = ta.ema(high, ma1_input)
ma2 = ta.ema(low, ma2_input)
// ma1   = ta.sma(close, ma1_input)
// ma2   = ta.sma(close,  ma2_input)
ma1op = ma1 * (1 + oppercent_1 / 100)
ma2op = ma2 * (1 + oppercent_1 / 100)
lastEntryPrice = strategy.opentrades.entry_price(strategy.opentrades - 1)
lastEntryPrice_sl = lastEntryPrice * (1 - slpercent_1 / 100)

//条件==========================================================================
long = close > ma1op
long_tp = close < ma2
// long    = close > ma1op or close > ma2op
// long_tp = close < ma1 and close < ma2 

//显示==========================================================================
stLongplot=plot(ma1, color = color.new(color.white, 0), title = 'MA1')
stShortplot=plot(ma2, color = color.new(color.red, 0), title = 'MA2')
fill(stLongplot,stShortplot,color= color.rgb(0,255,0,0.1))
plot(lastEntryPrice, color = color.new(color.white, 80), style = plot.style_linebr, linewidth = 1, title = '开仓价')
plot(lastEntryPrice_sl, color = color.new(color.red, 80), style = plot.style_linebr, linewidth = 1, title = '止损价')

//回测==========================================================================
ACT_BT = input.bool(true, title = '回测开关', group = '回测')
testStartYear = input.int(2023, title = '开始年份', minval = 1990, maxval = 2222, group = '回测')
testStartMonth = input.int(01, title = '开始月份', minval = 1, maxval = 12, group = '回测')
testStartDay = input.int(01, title = '开始日期', minval = 1, maxval = 31, group = '回测')
testPeriodStart = timestamp(testStartYear, testStartMonth, testStartDay, 0, 0)
testStopYear = input.int(2030, title = '终止年份', minval = 1980, maxval = 2222, group = '回测')
testStopMonth = input.int(12, title = '终止月份', minval = 1, maxval = 12, group = '回测')
testStopDay = input.int(31, title = '终止日期', minval = 1, maxval = 31, group = '回测')
testPeriodStop = timestamp(testStopYear, testStopMonth, testStopDay, 0, 0)
testPeriod = time >= testPeriodStart and time <= testPeriodStop ? true : false

//警报==========================================================================
alert_open = ' bot '

alert_close = ' bot '

//策略==========================================================================

if long and ACT_BT and testPeriod
    strategy.entry('开多', strategy.long, comment = '开仓' , alert_message = alert_open)
strategy.exit('止损', '开多', stop = lastEntryPrice_sl, comment = '❌' , alert_message = alert_close)

if long_tp
    strategy.close('开多', comment = '✅', alert_message = alert_close)
